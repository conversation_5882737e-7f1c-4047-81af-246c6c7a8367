<template>
  <div class="redirect-container">
    <div class="redirect-content">
      <!-- 登录失败状态 -->
      <div v-if="loginFailed" class="login-failed">
        <h1 class="redirect-title error-title">登录失败</h1>
        <p class="redirect-message error-message">很抱歉，登录验证失败，请重新尝试登录</p>
        <button class="back-to-login-btn" @click="backToLogin">返回登录页</button>
      </div>

      <!-- 正常跳转状态 -->
      <div v-else class="loading-state">
        <h1 class="redirect-title">跳转中...</h1>
        <p class="redirect-message">正在为您跳转到鹰眼系统，请稍候</p>

        <!-- 加载动画：旋转圆圈 -->
        <div class="loading-spinner"></div>

        <!-- 进度条 -->
        <div class="progress-container">
          <div class="progress-bar"></div>
        </div>

        <p class="redirect-tip">
          如果页面长时间未响应，请检查网络连接
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Jumps',
  data() {
    return {
      code: null,
      loginFailed: false // 控制登录失败状态
    }
  },
  mounted() {
    // 获取URL中的code参数
    const urlParams = new URLSearchParams(window.location.search)
    this.code = urlParams.get('code')
    console.log('获取到的code:', this.code)
    // 如果有code参数，立即调用login方法
    if (this.code) {
      this.login()
    } else {
      console.log('未获取到code参数')
    }

    // 可选：添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeUnmount() {
    // 清理事件监听器
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    handleKeydown(e) {
      if (e.key === 'Escape') {
        // ESC键取消跳转
        console.log('用户取消跳转')
      }
    },
    login() {
      console.log('this', this)
      console.log('this.$message:', this.$message)
      // 直接调用微信登录接口，不需要表单验证
      this.$http.get(`/user_info/wechat_login/?code=${this.code}`).then(async (result) => {
        const { data: res } = result
        console.log('登录响应:', res)
        if (res.meta.status !== 200) {
          this.$message.error({ message: res.meta.msg || '登录失败', duration: 3000 })
          // 设置登录失败状态，显示返回登录按钮
          this.loginFailed = true
          return // 直接返回
        }

        // 只有登录成功才执行以下代码
        window.localStorage.setItem('token', res.data.token)
        window.localStorage.setItem('userId', res.data.id)
        window.localStorage.setItem('user_name', res.data.user_name)
        window.localStorage.setItem('role_id', res.data.role_id)
        window.localStorage.setItem('chinese_name', res.data.chinese_name)
        window.localStorage.setItem('staff_no', res.data.staff_no)
        window.localStorage.setItem('can_add', res.data.can_add)

        // 重置重定向锁
        window.isRedirecting = false

        // 获取重定向路径并跳转
        const redirect = this.$route.query.url
        if (redirect) {
          sessionStorage.removeItem('redirect')
          setTimeout(() => {
            location.href = redirect
          }, 1000) // 延迟1秒跳转
        } else {
          this.$router.push('/home')
        }
      }).catch((error) => {
        console.error('接口调用失败:', error)
        this.$message.error('接口异常')
        this.loginFailed = true // 接口异常时也显示返回登录按钮
      })
    },
    // 返回登录页面
    backToLogin() {
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
/* 跳转中页面样式 - 白色背景黑色文字 */

.redirect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.redirect-content {
  text-align: center;
  color: #333333;
  padding: 40px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.redirect-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333333;
}

.redirect-message {
  font-size: 16px;
  margin-bottom: 30px;
  color: #666666;
  line-height: 1.5;
}

/* 加载动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top: 4px solid #333333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条样式 */
.progress-container {
  width: 200px;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  margin: 20px auto;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #333333, rgba(51, 51, 51, 0.8));
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

.redirect-tip {
  font-size: 14px;
  opacity: 0.6;
  margin-top: 20px;
  color: #999999;
}

/* 登录失败相关样式 */
.login-failed {
  text-align: center;
}

.error-title {
  color: #f56c6c;
  margin-bottom: 20px;
}

.error-message {
  color: #666666;
  margin-bottom: 30px;
}

.back-to-login-btn {
  background: #409EFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-to-login-btn:hover {
  background: #337ecc;
}

.back-to-login-btn:active {
  background: #2b6cb0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .redirect-content {
    padding: 30px 20px;
    margin: 20px;
  }

  .redirect-title {
    font-size: 20px
  }

  .redirect-message {
    font-size: 14px
  }
}

/* 淡入动画 */
.redirect-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.el-message {
  display: block !important;
  z-index: 10000 !important; /* 确保不被其他元素覆盖 */
}
</style>
