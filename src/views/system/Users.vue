<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>用户管理</el-breadcrumb-item>
      <el-breadcrumb-item>用户列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="7">
          <el-input placeholder="请输入用户ID"
                    v-model="queryInfo.userId"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="7">
          <el-input placeholder="请输入用户名称"
                    v-model="queryInfo.userName"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryUser">查 询</el-button>
        </el-col>
      </el-row>
        <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="addDialogVisible=true">添加用户</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="userList" border>
          <el-table-column label="ID" prop="id" min-width="50px"></el-table-column>
          <el-table-column label="用户名" prop="username" min-width="150px"></el-table-column>
          <el-table-column label="中文名" prop="chinese_name" min-width="100px"></el-table-column>
          <el-table-column label="邮箱" prop="email" min-width="200px"></el-table-column>
          <el-table-column label="电话" prop="mobile" width="200px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="150px">
            <template v-slot="slotProps">
            <el-button type="primary" icon="el-icon-edit" size="mini" @click="showEditDialog(slotProps.row)"></el-button>
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeUserById(slotProps.row.id)"></el-button>
            <el-tooltip effect="dark" content="分配权限" placement="top" :enterable="false">
              <el-button type="warning" icon="el-icon-setting" size="mini" @click="showAllotRoleDialog(slotProps.row)"></el-button>
            </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增用户对话框 -->
    <el-dialog :visible.sync="addDialogVisible"
                title="添加用户"
                width="50%"
                @close="addDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="addForm"
                label-width="80px"
                :rules="addFormRules"
                ref="addFormRef">
        <el-form-item label="工号" prop="staff_no">
          <el-input v-model="addForm.staff_no"></el-input>
        </el-form-item>
        <el-form-item label="用户名称" prop="username">
          <el-input v-model="addForm.username">
          </el-input>
        </el-form-item>
        <el-form-item label="中文名" prop="chinese_name">
          <el-input v-model="addForm.chinese_name">
          </el-input>
        </el-form-item>
        <el-form-item label="用户密码" prop="password">
          <el-input v-model="addForm.password"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="addForm.email"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="addForm.mobile"></el-input>
        </el-form-item>
        <el-form-item label="新建任务" prop="can_add">
          <el-switch v-model="addForm.can_add"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addUser">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑用户对话框 -->
    <el-dialog :visible.sync="editDialogVisible"
                title="编辑用户"
                width="50%"
                @close="editDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="editForm"
                label-width="80px"
                :rules="eidtFormRules"
                ref="editFormRef">
        <el-form-item label="工号" prop="mobile">
          <el-input v-model="editForm.staff_no" disabled></el-input>
        </el-form-item>
        <el-form-item label="用户名称" prop="username">
          <el-input v-model="editForm.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="中文名" prop="chinese_name">
          <el-input v-model="editForm.chinese_name">
          </el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="editForm.mobile"></el-input>
        </el-form-item>
        <el-form-item label="新建任务" prop="can_add">
          <el-switch v-model="editForm.can_add"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="eidtUser">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 分配权限对话框 -->
    <el-dialog :visible.sync="allotRoleDialogVisible"
                title="分配角色"
                width="50%"
                @close="allotRoleDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="allotRoleForm"
                label-width="100px"
                :rules="allotRoleFormRules"
                ref="allotRoleFormRef">
        <el-form-item label="用户名称：">
          {{ allotRoleForm.username }}
        </el-form-item>
        <el-form-item label="当前角色：">
          {{ allotRoleForm.role_name }}
        </el-form-item>
        <el-form-item label="分配角色" prop="rid">
          <el-select v-model="allotRoleForm.rid">
            <el-option v-for="item in roleList"
                       :key="item.id"
                       :value="item.id"
                       :label="item.label"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="allotRoleDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="allotRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    var checkMobile = (rule, value, callback) => {
      if (value === '') return callback()
      const regMobile = /^(0|86|17951)?(13[0-9]|15[012356789]|17[123456789]|18[0-9]|14[57])[0-9]{8}$/
      if (regMobile.test(value)) return callback()
      callback(new Error('请输入正确的手机号'))
    }
    return {
      model: 'user_info', // model名称,驼峰形式已“_"替代，如： userInfo 改为: user_info
      userList: [],
      queryInfo: {
        userId: null,
        userName: '',
        pagenum: 1,
        pagesize: 5
      },
      total: 0,
      addDialogVisible: false,
      addForm: {
        staff_no: '',
        username: '',
        chinese_name: '',
        password: '',
        email: '',
        mobile: '',
        can_add: false
      },
      editDialogVisible: false,
      editForm: {},
      addFormRules: {
        staff_no: [
          { required: true, message: '请输入工号', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名称', trigger: 'blur' }
        ],
        chinese_name: [
          { required: true, message: '请输入中文名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入用户密码', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        mobile: { validator: checkMobile, message: '请输入正确的手机号', trigger: 'blur' }
      },
      eidtFormRules: {
        staff_no: [
          { required: true, message: '请输入工号', trigger: 'blur' }
        ],
        chinese_name: [
          { required: true, message: '请输入中文名', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        mobile: { validator: checkMobile, message: '请输入正确的手机号', trigger: 'blur' }
      },
      select: '1',
      allotRoleDialogVisible: false,
      allotRoleForm: {},
      roleList: [],
      allotRoleFormRules: {
        rid: [
          { required: true, message: '请选择用户角色', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getUserList()
    this.getRoleList()
  },
  methods: {
    queryUser() {
      this.queryInfo.pagenum = 1
      this.getUserList()
    },
    async getUserList() {
      const { data: res } = await this.$http.get(this.model + '/users/', { params: this.queryInfo })
      console.log('用户列表==>', res)
      if (res.meta.status !== 200) return this.$message.error('获取用户列表失败')
      this.userList = res.data.users
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getUserList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getUserList()
    },
    async userStateChanged(userinfo) {
      const { data: res } = await this.$http.put(this.model + '/users/' + userinfo.id + '/state/' + userinfo.mg_state + '/')
      if (res.meta.status === 200) return this.$message.success('设置用户状态成功')
      userinfo.mg_state = !userinfo.mg_state
      this.$message.error('设置用户状态失败')
    },
    addDialogClosed() {
      this.$refs.addFormRef.resetFields()
    },
    addUser() {
      this.$refs.addFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.post(this.model + '/userAdd/', this.addForm)
        if (res.meta.status !== 200) return this.$message.error('新增用户失败')
        this.addDialogVisible = false
        this.$message.success('新增用户成功')
        this.getUserList()
      })
    },
    showEditDialog(data) {
      const objString = JSON.stringify(data)
      this.editForm = JSON.parse(objString)
      this.editDialogVisible = true
    },
    eidtUser() {
      this.$refs.editFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.put(this.model + '/' + this.editForm.id + '/user/', this.editForm)
        if (res.meta.status !== 200) return this.$message.error('编辑用户信息失败')
        this.editDialogVisible = false
        this.$message.success('编辑用户成功')
        this.getUserList()
      })
    },
    editDialogClosed() {
      this.$refs.editFormRef.resetFields()
    },
    removeUserById(id) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)

        this.$http.delete(this.model + '/' + id + '/delete_user/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除用户失败')
          this.$message.success('删除用户成功')
          this.getUserList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    allotRoleDialogClosed() {
      this.$refs.allotRoleFormRef.resetFields()
    },
    showAllotRoleDialog(userInfo) {
      const objString = JSON.stringify(userInfo)
      this.allotRoleForm = JSON.parse(objString)
      this.allotRoleDialogVisible = true
    },
    async getRoleList() {
      const { data: res } = await this.$http.get(this.model + '/info/', { params: { type: 'roles' } })
      if (res.meta.status !== 200) return this.$message.error('获取角色信息失败')
      this.roleList = res.data
    },
    allotRole() {
      this.$refs.allotRoleFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.put('role_info/' + this.allotRoleForm.id + '/user_role/', this.allotRoleForm)
        if (res.meta.status !== 200) return this.$message.error('设置角色失败')
        this.allotRoleDialogVisible = false
        this.getUserList()
      })
    }
  }
}
</script>
