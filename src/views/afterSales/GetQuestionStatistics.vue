<template>
  <div>
    <!-- AI回复数据统计 -->
    <el-card>
      <el-row>
        <div class="title">售后群AI回复数据统计
          <el-tooltip placement="top">
            <div slot="content">
              <p>1、统计产品线为微商城、OMS、四季蝉、海川、商品、组织、数仓、随心看，剩下的全部归类为其他</p>
              <p>2、数据筛选范围为【消息是否已推送】字段为【是】的数据</p>
              <p>3、只统计问题类型为咨询类、操作类的数据</p>
              <p>4、只统计【内部群】数据</p>
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-row>
      <el-row>
        <div style="margin:30px auto;">
          <el-row :gutter="20">
            <el-col :span="6" :offset="7">
             <el-date-picker
                v-model="queryAnswerStatisticDate"
                :clearable="false"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                disabledDate="false">
              </el-date-picker>
            </el-col>
            <el-col :span="3" :offset="2">
              <el-button type="primary" @click="getAnswerStatisticsCharts()">查 询</el-button>
            </el-col>
          </el-row>
        </div>
      </el-row>
      <el-row>
        <el-col>
          <div v-if="isAnswerStatisticsCharts" style="width:100%;height:400px;margin-top:30px">
            <el-empty :image-size="200"></el-empty>
          </div>
          <div v-show="!isAnswerStatisticsCharts" id="answerStatisticsCharts" style="width:100%;height:400px;margin-top:30px"></div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 售后群反馈趋势统计 -->
    <el-card>
      <el-row>
        <div class="title">售后群反馈趋势统计
          <el-tooltip placement="top">
            <div slot="content">
              <p>1、每周的统计周期为上周五~本周四</p>
              <p>2、统计产品线为微商城、OMS、四季蝉、海川、商品、组织、数仓、随心看，剩下的全部归类为其他</p>
              <p>3、只统计【内部群】数据</p>
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-row>
      <el-row>
        <div style="margin:30px auto;">
          <el-row :gutter="20">
            <el-col :span="6" :offset="7">
             <el-date-picker
                v-model="queryQuestionStatisticDate"
                :clearable="false"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                disabledDate="false">
              </el-date-picker>
            </el-col>
            <el-col :span="3" :offset="2">
              <el-button type="primary" @click="getQuestionStatisticsCharts()">查 询</el-button>
            </el-col>
          </el-row>
        </div>
      </el-row>
      <el-row>
        <el-col>
          <div v-if="isQuestionStatisticsCharts" style="width:100%;height:400px;margin-top:30px">
            <el-empty :image-size="200"></el-empty>
          </div>
          <div v-show="!isQuestionStatisticsCharts" id="questionStatisticsCharts" style="width:100%;height:400px;margin-top:30px"></div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 售后群问题处理及时率统计 -->
    <el-card>
      <el-row>
        <div class="title">售后群问题处理及时率统计
          <el-tooltip placement="top">
            <div slot="content">
              <p>1、数据筛选范围为【是否AI回答】字段为【否】，且【消息是否已推送】为【是】</p>
              <p>2、平均处理时长计算公式：处理完成时间-创建时间，获取处理人总时长后取平均值</p>
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-row>
      <el-row>
        <div style="margin:30px auto;">
          <el-row :gutter="20">
            <el-col :span="6" :offset="4">
             <el-date-picker
                v-model="queryQuestionTimeStatisticDate"
                :clearable="false"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                disabledDate="false">
              </el-date-picker>
            </el-col>
            <el-col :span="5" :offset="2">
              <el-input placeholder="处理人姓名或工号"
                    v-model="comp_user"
                    clearable
                    >
              </el-input>
            </el-col>
            <el-col :span="3" :offset="1">
              <el-button type="primary" @click="getQuestionTimeStatisticsCharts()">查 询</el-button>
            </el-col>
          </el-row>
        </div>
      </el-row>
      <el-row>
        <el-col>
          <div v-if="isQuestionTimeStatisticsCharts" style="width:100%;height:400px;margin-top:30px">
            <el-empty :image-size="200"></el-empty>
          </div>
          <div v-show="!isQuestionTimeStatisticsCharts" id="questionTimeStatisticsCharts" :style="{ width: '100%', height: chartHeight + 'px', marginTop: '30px' }"></div>
<!--          <div id="questionTimeStatisticsCharts" style="width:100%;height:400px;margin-top:30px"></div>-->
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isAnswerStatisticsCharts: false,
      isQuestionStatisticsCharts: false,
      isQuestionTimeStatisticsCharts: false,
      chartHeight: 400, // 初始高度
      model: 'question',
      data: {},
      update_time: '',
      comp_user: '',
      queryAnswerStatisticDate: [], // AI回复数据统计查询时间
      queryQuestionStatisticDate: [], // 售后群反馈趋势统计查询时间
      queryQuestionTimeStatisticDate: [], // 售后群反馈趋势统计查询时间
      dateRange: {
        start_date: '',
        end_date: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {
    this.formattedDate()
    this.getAnswerStatisticsCharts()
    this.getQuestionStatisticsCharts()
    this.getQuestionTimeStatisticsCharts()
  },
  activated() {
    this.comp_user = ''
    this.formattedDate()
    if (this.answerStatisticsCharts) {
      this.answerStatisticsCharts.resize()
    }
    if (this.questionStatisticsCharts) {
      this.questionStatisticsCharts.resize()
    }
    if (this.chartInstance) {
      this.chartInstance.resize()
    }
  },
  methods: {
    formattedDate() {
      const formatDate = date => date.toLocaleDateString().replace(/\//g, '-')
      const starttime = new Date()
      starttime.setMonth(starttime.getMonth() - 3)
      const endtime = new Date()
      const formattedStart = formatDate(starttime)
      const formattedEnd = formatDate(endtime)
      this.queryAnswerStatisticDate =
      this.queryQuestionStatisticDate =
      this.queryQuestionTimeStatisticDate = [formattedStart, formattedEnd]
    },
    async getAnswerStatisticsCharts() {
      const { data: res } = await this.$http.get(this.model + '/get_answer_statistics/', {
        params: { start_date: this.queryAnswerStatisticDate[0], end_date: this.queryAnswerStatisticDate[1] }
      })
      if (res.meta.status !== 200) return this.$message.error('获取AI回复数据统计失败')
      const answerStatisticsCharts = this.$echarts.init(document.getElementById('answerStatisticsCharts'))
      const xData = res.data.map(function (item) {
        return item.project_name
      })
      if (
        !xData.length
      ) {
        answerStatisticsCharts.clear() // 清除旧图表
        // 获取图表的容器元素
        this.isAnswerStatisticsCharts = true
        return
      }
      this.isAnswerStatisticsCharts = false
      // 获取人工回答数据
      const seriesDataManual = res.data.map(item => ({
        value: item.manual_send_count,
        project_name: item.project_name
      }))
      // 获取 AI 回答数据
      const seriesDataAI = res.data.map(item => ({
        value: item.auto_send_count, // 这里添加 AI 回答的数量
        project_name: item.project_name
      }))
      const option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          enterable: true,
          formatter: function (params) {
            let tooltipContent = ''
            params.forEach(param => {
              const data = param.data
              if (param.seriesName === 'AI回答') {
                tooltipContent += `业务线: ${data.project_name}<br>`
                tooltipContent += `<span class='dot'></span>AI回答: ${data.value}<br>`
              } else if (param.seriesName === '人工回答') {
                tooltipContent += `<span class='dot'></span>人工回答: ${data.value}<br>`
              }
            })
            return tooltipContent
          },
          backgroundColor: 'rgba(0,0,0,0.75)',
          borderColor: 'rgba(0,0,0,0.75)',
          textStyle: {
            color: '#fff',
            fontsize: '14',
            width: 10,
            height: 10,
            overflow: 'break'
          }
        },
        legend: {
        },
        xAxis: {
          data: xData,
          // x轴文字配置
          axisLabel: {
            interval: 0,
            width: 50,
            overflow: 'truncate'
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}' // 默认显示为整数
          }
        },
        series: [
          {
            name: 'AI回答',
            type: 'bar',
            data: seriesDataAI,
            barWidth: 45, // 设置柱状图的宽度
            itemStyle: {
              color: 'rgba(78,142,247,0.87)' // 设置AI柱状图的颜色
            },
            label: {
              show: true, // 显示数值
              position: 'top' // 位置（可选：inside, outside, top, bottom, left, right）
            }
          },
          {
            name: '人工回答',
            type: 'bar',
            data: seriesDataManual,
            barWidth: 45, // 设置柱状图的宽度
            itemStyle: {
              color: '#f5a871' // 设置AI柱状图的颜色
            },
            label: {
              show: true, // 显示数值
              position: 'top' // 位置（可选：inside, outside, top, bottom, left, right）
            }
          }
        ]
      }
      answerStatisticsCharts.clear()
      answerStatisticsCharts.setOption(option)
      this.$nextTick(() => {
        answerStatisticsCharts.resize()
      })
      // 监听浏览器窗口大小变化,当窗口大小改变时,answerStatisticsCharts.resize()方法来调整图标大小
      window.addEventListener('resize', () => {
        answerStatisticsCharts.resize() // myChart是上面定义的
      })
      this.answerStatisticsCharts = answerStatisticsCharts
    },
    async getQuestionStatisticsCharts() {
      const { data: res } = await this.$http.get(this.model + '/get_question_statistics/', {
        params: { start_date: this.queryQuestionStatisticDate[0], end_date: this.queryQuestionStatisticDate[1] }
      })
      const questionStatisticsCharts = this.$echarts.init(document.getElementById('questionStatisticsCharts'))

      // 提取所有周数作为 x 轴数据
      const xAxisData = res.data?.[0]?.dates?.map(item => item.date) ?? []
      if (
        !xAxisData.length
      ) {
        questionStatisticsCharts.clear() // 清除旧图表
        // 获取图表的容器元素
        this.isQuestionStatisticsCharts = true
        return
      }

      this.isQuestionStatisticsCharts = false
      // 处理每个业务线的数据
      const seriesData = res.data.map(item => ({
        name: item.project_type, // 业务线名称
        type: 'line', // 折线图
        data: item.dates.map(dateItem => dateItem.count) // 每周的问题数量
      }))
      // 配置 ECharts 选项
      const option = {
        tooltip: {
          trigger: 'axis', // 触发方式为坐标轴
          formatter: function (params) {
            let total = 0
            let tooltipContent = params[0].name + '<br>' // 周数
            params.forEach(param => {
              tooltipContent += `<span class='dot'></span>${param.seriesName}: ${param.value}<br>` // 业务线名称和问题数量
              total += param.value || 0
            })
            tooltipContent += `<strong><span class='total' ></span>问题总数: ${total}</strong><br>`
            return tooltipContent
          }
        },
        legend: {
          data: res.data.map(item => item.project_type) // 图例数据（业务线名称）
        },
        xAxis: {
          type: 'category', // 类目轴
          data: xAxisData, // 周数
          axisLabel: {
            rotate: 45 // 如果周数过多，可以旋转 45 度
          }
        },
        yAxis: {
          type: 'value', // 数值轴
          name: '问题数量'
        },
        series: seriesData // 系列数据
      }
      // 设置配置项并渲染图表
      questionStatisticsCharts.clear()
      questionStatisticsCharts.setOption(option)
      this.$nextTick(() => {
        questionStatisticsCharts.resize()
      })
      // 窗口大小变化时，调整图表大小
      window.addEventListener('resize', () => {
        questionStatisticsCharts.resize()
      })
      this.questionStatisticsCharts = questionStatisticsCharts
    },
    async getQuestionTimeStatisticsCharts() {
      const params = {
        start_date: this.queryQuestionTimeStatisticDate[0],
        end_date: this.queryQuestionTimeStatisticDate[1]
      }
      if (this.comp_user) {
        params.comp_user = this.comp_user
      }
      const { data: res } = await this.$http.get(this.model + '/get_question_time_statistics/', { params: params })
      // 动态计算容器高度：每个处理人占70px
      if (res.data.length < 4) {
        this.chartHeight = 300
      } else {
        this.chartHeight = res.data.length * 70
      }
      await this.$nextTick() // 确保DOM更新
      const chartDom = document.getElementById('questionTimeStatisticsCharts')
      let chartInstance = this.$echarts.getInstanceByDom(chartDom)
      if (!chartInstance) {
        chartInstance = this.$echarts.init(chartDom)
      }
      // 提取所有处理人作为  轴数据
      const yAxisData = res.data.map(item => item.comp_name)
      if (
        !yAxisData.length
      ) {
        chartInstance.clear() // 清除旧图表
        // 获取图表的容器元素
        this.isQuestionTimeStatisticsCharts = true
        return
      }
      this.isQuestionTimeStatisticsCharts = false
      // 提取平均处理时长作为 x 轴数据
      const seriesData = res.data.map(item => item.avg_time)
      // 配置 ECharts 选项
      const option = {
        tooltip: {
          trigger: 'axis', // 触发方式为坐标轴
          formatter: function (params) {
            const data = params[0].data
            return `处理人: ${params[0].name}<br><span class='dot'></span>平均处理时长: ${data} 分钟`
          }
        },
        legend: {
          data: ['平均处理时长(分钟)'], // 图例名称要与 series 里的 name 对应
          top: 'top', // 图例位置，可根据需求调整
          textStyle: {
            color: '#333' // 图例文字颜色
          }
        },
        xAxis: {
          type: 'value' // 数值轴
        },
        yAxis: {
          type: 'category', // 类目轴
          data: yAxisData // 处理人姓名
        },
        series: [
          {
            name: '平均处理时长(分钟)',
            type: 'bar', // 柱状图
            barWidth: 30, // 设置柱状图的宽度
            data: seriesData, // 平均处理时长数据
            itemStyle: {
              color: '#5470C6' // 柱状颜色
            },
            label: {
              show: true, // 显示数值
              position: 'right' // 位置（可选：inside, outside, top, bottom, left, right）
            }
          }
        ],
        grid: {
          left: '2%', // 调整左侧留白
          right: '13%', // 调整右侧留白
          bottom: '3%', // 调整底部留白
          containLabel: true // 包含 y 轴标签
        }
      }
      chartInstance.clear()
      chartInstance.setOption(option)
      this.$nextTick(() => {
        chartInstance.resize()
      })
      // 窗口缩放监听
      window.addEventListener('resize', () => chartInstance.resize())
      this.chartInstance = chartInstance
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  width: 100%;
  height:20px;
  line-height:20px;
  font-size: 20px;
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
  font-weight: 700;
  color: black;
}

.time {
  width: 100%;
  height: 14px;
  line-height: 14px;
  font-size: 14px;
  text-align: center;
  color: rgb(74, 74, 74);
  margin-top: 10px;
}

.data-margin {
  width: 90%;
  margin: auto;
  margin-top: 20px;
}

.el-card {
  color: #fff;
  border-radius: 20px;
  margin-bottom: 25px;
}

.top-card {
  background: #4F88FF;
}

.top-card .center-data {
  background: #2663E2;
}

.center-data {
  width: 80%;
  margin: 10px auto 10px auto;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  font-family: PingFangTC-Semibold, PingFangTC;
  font-weight: 600;
}

.maru {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  background: #FFFFFF;
  margin-right: 10px;
}

.item {
  text-align: center;
}

.bottom-card {
  background: #F6B03E;
}

.bottom-card .center-data {
  background: #E0590B;
}
/deep/ .dot {
  width: 10px; height: 10px; background: #a1cc95; display: inline-block; border-radius: 100px; margin: 0 6px 0 0;
}
/deep/ .total {
  width: 10px; height: 10px; background: #f5a871; display: inline-block; border-radius: 100px; margin: 0 6px 0 0;
}
</style>
