<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>任务管理</el-breadcrumb-item>
      <el-breadcrumb-item>处理任务</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <!-- 查询表单第一行 -->
      <el-form :inline="true" :model="queryInfo" class="query-form">
        <el-row :gutter="15">
          <el-col :span="5">
            <el-input placeholder="任务名称"
                      v-model="queryInfo.title"
                      clearable
                      @keyup.enter.native="queryList">
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="任务描述"
                      v-model="queryInfo.description"
                      clearable
                      @keyup.enter.native="queryList">
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select placeholder="优先级"
                       v-model="queryInfo.priority"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="紧急"></el-option>
              <el-option value="1" label="高"></el-option>
              <el-option value="2" label="中"></el-option>
              <el-option value="3" label="低"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select placeholder="任务状态"
                       v-model="queryInfo.status"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="进行中"></el-option>
              <el-option value="1" label="已完成"></el-option>
              <el-option value="2" label="已关闭"></el-option>
              <el-option value="3" label="已延期"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select placeholder="任务类型"
                       v-model="queryInfo.category"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="客户经营"></el-option>
              <el-option value="1" label="工单处理"></el-option>
              <el-option value="2" label="款项跟进"></el-option>
              <el-option value="3" label="公司事项"></el-option>
              <el-option value="4" label="其他"></el-option>
            </el-select>
          </el-col>
        </el-row>

        <!-- 操作按钮行 -->
        <el-row :gutter="15" style="margin-top: 10px;">
          <el-col :span="4">
            <el-input placeholder="发布人"
                      v-model="queryInfo.send_from"
                      clearable
                      @keyup.enter.native="queryList">
            </el-input>
          </el-col>
          <el-col :span="7">
            <el-form-item label="创建时间" style="margin-bottom: 0;">
              <el-date-picker
                v-model="queryCreateDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
                style="width: 270px;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="截止时间" style="margin-bottom: 0;">
              <el-date-picker
                v-model="queryDeadlineDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDeadlineDateChange"
                style="width: 270px;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="display: flex; align-items: center; justify-content: flex-end; gap: 8px;">
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button type="primary" @click="queryReset">重置</el-button>
            <el-button type="primary" @click="queryExport">导出</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 任务列表表格 -->
      <el-row>
        <el-table
          :data="assignmentList"
          stripe
          border
          :header-cell-style="{
            backgroundColor: '#e6f7ff',
            color: '#606266',
            fontSize: '12px',
            fontWeight: 'bold'
          }">

          <el-table-column label="任务名称" prop="title" min-width="150px" align="center"></el-table-column>
          <el-table-column min-width="250px">
            <template #header>
              <span>
                任务描述
                <el-tooltip content="点击单元格可查看任务描述详情" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
                </el-tooltip>
              </span>
            </template>
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover">
                <div style="white-space: pre-line; font-size: 12px">{{ scope.row.description }}</div>
                <div slot="reference"
                     class="cell-content"
                     style="cursor: pointer;">
                  <div style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    {{ scope.row.description }}
                  </div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="任务类型" min-width="90px" align="center">
            <template #default="scope">
              {{ getAssignmentTypeLabel(scope.row.category) }}
            </template>
          </el-table-column>
          <el-table-column label="优先级" min-width="70px" align="center">
            <template #default="scope">
              <el-tag :type="getPriorityTagType(scope.row.priority)">
                {{ getPriorityLabel(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="图片" min-width="90px" align="center">
            <template #default="scope">
              <div v-if="scope.row.attachment_url && scope.row.attachment_url.length > 0">
                <el-link @click="previewImages(scope.row.attachment_url)" style="font-weight: normal; font-size: x-small">
                  查看图片({{ scope.row.attachment_url.length }})
                </el-link>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="任务状态" min-width="80px" align="center">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.sub_status[0].status)">
                {{ getStatusLabel(scope.row.sub_status[0].status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发布人" min-width="100px" align="center">
            <template #default="scope">
              {{ scope.row.send_from_name }}({{ scope.row.send_from }})
            </template>
          </el-table-column>
          <el-table-column label="任务创建时间" show-overflow-tooltip prop="create_time" min-width="150px" align="center"></el-table-column>
          <el-table-column label="任务截止时间" show-overflow-tooltip prop="deadline" min-width="150px" align="center"></el-table-column>
          <el-table-column label="操作" align="center" min-width="50px">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="任务详情" placement="top" :enterable="false">
                <el-button circle type="primary" icon="el-icon-edit" size="mini" @click="viewAssignmentDetail(slotProps.row)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <!-- 分页 -->
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>

      <!-- 图片预览弹框 -->
      <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 自定义关闭按钮 -->
          <div @click="closeImagePreview"
               style="position: absolute; top: 20px; right: 20px; cursor: pointer; color: white; font-size: 24px; background: rgba(0,0,0,0.6); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; z-index: 1000; transition: background-color 0.3s;"
               @mouseenter="$event.target.style.backgroundColor = 'rgba(0,0,0,0.8)'"
               @mouseleave="$event.target.style.backgroundColor = 'rgba(0,0,0,0.6)'">
            &#10005;
          </div>
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;">
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 任务详情弹框 -->
      <el-dialog
        title="任务详情"
        :visible.sync="showAssignmentDetailDialog"
        width="1200px"
        height="80%"
        :close-on-click-modal="false"
        @close="handleAssignmentDetailDialogClose">
        <!-- 任务基本信息 -->
        <div v-if="assignmentDetail" class="assignment-detail-header">
          <el-row :gutter="20" style="margin-top:10px; margin-left: 30px">
            <el-col :span="12">
              <div class="info-item">
                <label style="font-size: 15px">任务下发时间：</label>
                <span>{{ assignmentDetail.create_time }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label style="font-size: 15px">任务截止时间：</label>
                <span>{{ assignmentDetail.deadline }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-left: 30px">
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">优先级：</label>
                <el-tag :type="getPriorityTagType(assignmentDetail.priority)">
                  {{ getPriorityLabel(assignmentDetail.priority) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">任务类型：</label>
                <span class="field-text-content">{{ getAssignmentTypeLabel(assignmentDetail.category) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">任务图片：</label>
                <div v-if="assignmentDetail.attachment_url && assignmentDetail.attachment_url.length > 0">
                  <el-button type="text" @click="previewImages(assignmentDetail.attachment_url)" class="field-button-content">
                    查看图片({{ assignmentDetail.attachment_url.length }})
                  </el-button>
                </div>
                <span v-else class="field-text-content">无</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-left: 30px">
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">任务状态：</label>
                <el-tag :type="getStatusTagType(assignmentDetail.comp_infos[0].status)">
                  {{ getStatusLabel(assignmentDetail.comp_infos[0].status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">处理时长：</label>
                <span class="field-text-content">{{ assignmentDetail.comp_infos[0].processing_time }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">是否延期：</label>
                <el-tag :type="assignmentDetail.comp_infos[0].is_delayed ? 'danger' : 'success'">
                  {{ assignmentDetail.comp_infos[0].is_delayed ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6" v-if="assignmentDetail.comp_infos[0].is_delayed">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">延期时长：</label>
                <span class="field-text-content" style="color: #FF0000">{{ assignmentDetail.comp_infos[0].delay_time }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row  :gutter="20" style="margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <label style="font-size: 15px">任务名称：</label>
                <span style="font-weight: bold;">{{ assignmentDetail.title }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row  :gutter="20" style="margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <label style="font-size: 15px">任务描述：</label>
                <div style="white-space: pre-line; margin-top: 5px; padding: 10px; background-color: #f5f7fa; border-radius: 4px;">
                  {{ assignmentDetail.description }}
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 任务回复区域 -->
          <el-row :gutter="20" style="margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <span style="color: #FF0000;">* </span><label style="font-size: 15px">任务回复：</label>
                <span style="font-style: italic; font-size: 12px; color: #f5a871;" v-if="assignmentDetail.status === 2">【当前主任务已关闭，不支持对任务进行再次操作】</span>
                <el-input
                  type="textarea"
                  v-model="replyForm.reply_content"
                  placeholder="请输入任务回复内容"
                  :disabled="assignmentDetail.status === 2"
                  :rows="4"
                  maxlength="2000"
                  show-word-limit
                  @paste.native="handleReplyDescriptionPaste"
                  ref="replyDescriptionInput"
                  style="margin-top: 5px;">
                </el-input>
              </div>
            </el-col>
          </el-row>

          <!-- 图片上传区域 -->
          <el-row :gutter="20" style=" margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <label style="font-size: 15px">上传图片：</label>
                <el-upload
                  class="upload-demo"
                  :action="replyUploadPath"
                  list-type="picture-card"
                  :multiple="true"
                  :disabled="assignmentDetail.status === 2"
                  :limit="3"
                  :file-list="replyUploadFileList"
                  :on-preview="handleReplyImagePreview"
                  :on-remove="handleReplyImageRemove"
                  :on-success="handleReplyImageUploadSuccess"
                  :on-exceed="handleReplyImageExceed"
                  :before-upload="beforeReplyImageUpload"
                  :on-change="handleReplyImageList"
                  :auto-upload="true"
                  style="margin-top: 5px;">
                  <i class="el-icon-plus"></i>
                  <div slot="tip" class="el-upload__tip">
                    支持jpg/png格式，单张图片不超过5MB，最多3张图片<br>
                    支持在任务回复中直接粘贴图片（Ctrl+V）<br>
                    <span :style="{ color: replyForm.attachment_url.length >= 3 ? '#F56C6C' : '#909399' }">
                      当前已上传：{{ replyForm.attachment_url.length }}/3 张图片
                    </span>
                  </div>
                </el-upload>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style=" margin-left: 30px; display: flex; align-items: center; justify-content: flex-end; gap: 8px;">
            <el-button
              type="success"
              :disabled="isCurrentUserTaskCompleted"
              @click="completeTask">
              完成任务
            </el-button>
            <el-button type="primary" :disabled="assignmentDetail.status === 2" @click="saveTaskProgress">保存</el-button>
            <el-button @click="showAssignmentDetailDialog = false">关 闭</el-button>
          </el-row>
        </div>
      </el-dialog>

    </el-card>
  </div>
</template>

<script>

export default {
  data() {
    return {
      model: 'assignment',
      queryCreateDate: this.getLastMonthRange(),
      queryDeadlineDate: [],
      assignmentList: [], // 任务列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: { // 查询栏
        title: '', // 任务标题
        description: '', // 任务描述
        priority: '', // 优先级
        status: '', // 任务状态
        category: '', // 任务类型
        start_date: '', // 发布时间-开始时间
        end_date: '', // 发布时间-结束时间
        deadline_start: '', // 截止时间-开始时间
        deadline_end: '', // 截止时间-结束时间
        pagenum: 1,
        pagesize: 20
      },
      total: 0,
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      // 新增任务相关数据
      showAddAssignmentDialog: false, // 控制新增任务弹框显示
      showConfirmDialog: false, // 控制二次确认弹框显示
      assignmentForm: {
        title: '',
        description: '',
        priority: '',
        category: '',
        comp_users: [], // 处理人列表
        deadline: '',
        attachment_url: [] // 上传的图片URL列表
      },
      // 人员选择相关
      employeeOptions: [], // 员工选项列表
      employeeLoading: false, // 员工搜索加载状态
      // 图片上传相关
      uploadFileList: [], // 上传文件列表
      maxPersonLimit: 100, // 最大发送人数限制
      // 任务详情相关
      showAssignmentDetailDialog: false, // 控制任务详情弹框显示
      assignmentDetail: null, // 当前查看的任务详情
      // 任务回复相关
      replyForm: {
        reply_content: '', // 任务回复内容
        attachment_url: [] // 回复图片URL列表
      },
      replyUploadFileList: [] // 回复图片上传文件列表
    }
  },
  /**
   * 组件创建时的生命周期钩子
   * 初始化默认日期并获取任务列表
   */
  created() {
    this.default_date()
    this.getAssignmentList()
  },
  /**
   * 组件挂载后的生命周期钩子
   * 添加键盘事件监听器用于图片预览功能
   */
  mounted() {
    // 添加键盘事件监听器
    document.addEventListener('keydown', this.handleKeydown)
  },
  /**
   * 组件销毁前的生命周期钩子
   * 移除键盘事件监听器防止内存泄漏
   */
  beforeDestroy() {
    // 移除键盘事件监听器
    document.removeEventListener('keydown', this.handleKeydown)
  },
  computed: {
    /**
     * 当前预览图片的URL
     * @returns {string} 当前图片的URL或空字符串
     */
    currentImageSrc() {
      return this.previewImagesList[this.currentImageIndex] || ''
    },
    /**
     * 任务图片上传路径
     * @returns {string} 完整的图片上传API路径
     */
    assignmentUploadPath() {
      return `${this.$http.defaults.baseURL || ''}question/question_attachment_upload/`
    },
    /**
     * 任务回复图片上传路径
     * @returns {string} 完整的图片上传API路径
     */
    replyUploadPath() {
      return `${this.$http.defaults.baseURL || ''}question/question_attachment_upload/`
    },
    /**
     * 判断当前用户的任务是否已完成
     * @returns {boolean} 如果当前用户的任务状态为已完成则返回true，否则返回false
     */
    isCurrentUserTaskCompleted() {
      if (!this.assignmentDetail || !this.assignmentDetail.comp_infos) {
        return false
      }

      // 查找当前用户的任务处理信息
      const currentUserInfo = this.assignmentDetail.comp_infos.find(info =>
        info.comp_user === this.staff_no
      )

      // 如果找到当前用户的信息且状态为已完成(1)，或者当前任务状态为已关闭,则禁用按钮
      return (currentUserInfo && currentUserInfo.status === 1) || (this.assignmentDetail.status === 2)
    }
  },
  methods: {
    // ==================== 查询和列表管理相关方法 ====================

    /**
     * 查询任务列表
     * 重置页码为1并获取任务列表数据
     * @returns {void}
     */
    queryList() {
      this.queryInfo.pagenum = 1
      this.getAssignmentList()
    },

    /**
     * 重置查询条件
     * 将所有查询条件重置为默认值并重新获取数据
     * @returns {void}
     */
    queryReset() {
      this.queryCreateDate = this.getLastMonthRange()
      this.queryDeadlineDate = []
      this.queryInfo = {
        title: '',
        description: '',
        priority: '',
        status: '',
        category: '',
        start_date: '', // 创建时间-开始时间
        end_date: '', // 创建时间-结束时间
        deadline_start: '', // 截止时间-开始时间
        deadline_end: '', // 截止时间-结束时间
        pagenum: 1,
        pagesize: 20
      }
      this.default_date()
      this.getAssignmentList()
    },

    /**
     * 获取任务列表数据
     * 调用后端API获取任务列表，根据当前查询条件进行筛选
     * @returns {Promise<void>} 无返回值，结果存储在assignmentList中
     * @throws {Error} 当API调用失败时在控制台输出警告
     */
    async getAssignmentList() {
      try {
        this.queryInfo.pagefrom = 'handle_tasks'
        const { data: res } = await this.$http.post(this.model + '/assignment_list/', this.queryInfo)
        if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
        this.total = res.data.total
        this.assignmentList = res.data.assignments.map(item => ({
          ...item
        }))
      } catch (error) {
        console.warn('API调用失败:', error)
      }
    },

    /**
     * 导出任务列表到Excel文件
     * 调用后端接口导出当前查询条件下的所有任务数据
     * @returns {Promise<void>} 无返回值，成功时自动下载文件
     * @throws {Error} 当导出失败时显示错误消息
     */
    async queryExport() {
      try {
        // 深拷贝 queryInfo，避免修改原始对象
        const exportQueryInfo = JSON.parse(JSON.stringify(this.queryInfo))
        exportQueryInfo.pagesize = 10000
        const response = await this.$http.post(this.model + '/export_assignment_data/', exportQueryInfo, {
          responseType: 'blob'
        })
        // 检查响应是否为 Blob 类型
        if (response.data instanceof Blob && response.data.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          const filename = '任务列表.xlsx'
          await this.downloadFile(response, filename)
        } else {
          // 如果不是预期的 Excel 文件，尝试解析为 JSON
          const text = await response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    /**
     * 下载文件到本地
     * 创建临时下载链接并触发文件下载
     * @param {Object} response - HTTP响应对象，包含文件数据
     * @param {string} filename - 下载文件的名称
     * @returns {Promise<void>} 无返回值，成功时显示成功消息
     */
    async downloadFile(response, filename) {
      const blob = response.data
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      this.$message.success('导出成功')
    },

    /**
     * 处理分页大小变化事件
     * 当用户改变每页显示条数时触发
     * @param {number} newSize - 新的每页显示条数
     * @returns {void}
     */
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.queryInfo.pagenum = 1
      this.getAssignmentList()
    },

    /**
     * 处理页码变化事件
     * 当用户切换页面时触发
     * @param {number} newPage - 新的页码
     * @returns {void}
     */
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getAssignmentList()
    },

    /**
     * 处理创建日期范围变化事件
     * 当用户选择创建时间范围时触发，更新查询条件
     * @param {Array|null} value - 日期范围数组[开始日期, 结束日期]，或null
     * @returns {void}
     */
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.queryInfo.start_date = value[0]
        this.queryInfo.end_date = value[1]
      } else {
        this.queryInfo.start_date = ''
        this.queryInfo.end_date = ''
      }
    },

    /**
     * 处理截止日期范围变化事件
     * 当用户选择截止时间范围时触发，更新查询条件
     * @param {Array|null} value - 日期范围数组[开始日期, 结束日期]，或null
     * @returns {void}
     */
    handleDeadlineDateChange(value) {
      if (value && value.length === 2) {
        this.queryInfo.deadline_start = value[0]
        this.queryInfo.deadline_end = value[1]
      } else {
        this.queryInfo.deadline_start = ''
        this.queryInfo.deadline_end = ''
      }
    },

    /**
     * 设置默认查询日期
     * 根据queryCreateDate设置查询条件中的开始和结束日期
     * @returns {void}
     */
    default_date() {
      if (this.queryCreateDate && this.queryCreateDate.length === 2) {
        this.queryInfo.start_date = this.queryCreateDate[0]
        this.queryInfo.end_date = this.queryCreateDate[1]
      }
    },

    // ==================== 任务基本操作方法 ====================
    /**
     * 查看任务详情
     * 获取指定任务的详细信息并打开详情弹框
     * @param {Object} assignment - 任务对象，包含任务ID等信息
     * @returns {Promise<void>} 无返回值，成功时打开详情弹框
     * @throws {Error} 当获取详情失败时在控制台输出错误
     */
    async viewAssignmentDetail(assignment) {
      try {
        // 调用详情接口获取任务详情
        const { data: res } = await this.$http.get(`${this.model}/assignment_detail/?id=${assignment.id}&pagefrom=handle_tasks`)

        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '获取任务详情失败')
        }

        this.assignmentDetail = res.data

        // 查找当前用户的任务处理信息，并加载到回复表单中
        if (this.assignmentDetail.comp_infos) {
          const currentUserInfo = this.assignmentDetail.comp_infos.find(info =>
            info.comp_user === this.staff_no
          )

          if (currentUserInfo) {
            // 回显回复内容
            this.replyForm.reply_content = currentUserInfo.reply_content || ''

            // 回显回复图片
            this.replyForm.attachment_url = currentUserInfo.attachment_url || []

            // 更新上传文件列表以显示已有图片，参考questionList.vue的实现方式
            this.replyUploadFileList = (currentUserInfo.attachment_url || []).map((url, index) => ({
              name: url.split('/').pop() || `image-${index + 1}`, // 使用文件名或默认名称
              url: url,
              status: 'success', // 设置文件状态为成功
              uid: Date.now() + index
            }))
          } else {
            // 如果没有找到当前用户的信息，重置表单
            this.resetReplyForm()
            return this.$message.error('任务数据异常')
          }
        } else {
          // 如果没有comp_infos，重置表单
          this.resetReplyForm()
        }

        this.showAssignmentDetailDialog = true
      } catch (error) {
        console.error('获取任务详情失败:', error)
      }
    },

    /**
     * 关闭任务详情弹框
     * 重置所有任务详情相关的数据状态
     * @returns {void}
     */
    handleAssignmentDetailDialogClose() {
      this.assignmentDetail = null
      // 重置回复表单
      this.resetReplyForm()
    },

    // ==================== 工具和辅助方法 ====================

    /**
     * 获取最近一个月的日期范围
     * 计算从30天前到今天的日期范围
     * @returns {Array<string>} 格式化后的日期范围数组[开始日期, 结束日期]
     */
    getLastMonthRange() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [this.formatDate(start), this.formatDate(end)]
    },

    /**
     * 格式化日期为YYYY-MM-DD格式
     * @param {Date} date - 需要格式化的日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 获取任务类型标签文本
     * 根据任务类型数值返回对应的中文标签
     * @param {number} assignmentType - 任务类型数值(0-4)
     * @returns {string} 任务类型的中文标签
     */
    getAssignmentTypeLabel(assignmentType) {
      const labelMap = {
        0: '客户经营',
        1: '工单处理',
        2: '款项跟进',
        3: '公司事项',
        4: '其他'
      }
      return labelMap[assignmentType] || '其他'
    },
    /**
     * 获取优先级标签样式类型
     * 根据优先级数值返回对应的Element UI标签样式
     * @param {number} priority - 优先级数值(0-3)
     * @returns {string} Element UI标签样式类型
     */
    getPriorityTagType(priority) {
      const typeMap = {
        0: 'danger',
        1: 'warning',
        2: 'primary',
        3: 'info'
      }
      return typeMap[priority] || 'info'
    },
    /**
     * 获取优先级标签文本
     * 根据优先级数值返回对应的中文标签
     * @param {number} priority - 优先级数值(0-3)
     * @returns {string} 优先级的中文标签
     */
    getPriorityLabel(priority) {
      const labelMap = {
        0: '紧急',
        1: '高',
        2: '中',
        3: '低'
      }
      return labelMap[priority] || '低'
    },
    /**
     * 获取任务状态标签样式类型
     * 根据任务状态数值返回对应的Element UI标签样式
     * @param {number} status - 任务状态数值(0-3)
     * @returns {string} Element UI标签样式类型
     */
    getStatusTagType(status) {
      const typeMap = {
        0: 'primary',
        1: 'success',
        2: 'info',
        3: 'danger'
      }
      return typeMap[status] || 'primary'
    },
    /**
     * 获取任务状态标签文本
     * 根据任务状态数值返回对应的中文标签
     * @param {number} status - 任务状态数值(0-3)
     * @returns {string} 任务状态的中文标签
     */
    getStatusLabel(status) {
      const labelMap = {
        0: '进行中',
        1: '已完成',
        2: '已关闭',
        3: '已延期'
      }
      return labelMap[status] || '进行中'
    },

    /**
     * 预览图片
     * 打开图片预览弹框并显示指定的图片列表
     * @param {Array<string>} images - 图片URL数组
     * @returns {void}
     */
    previewImages(images) {
      this.previewImagesList = images
      this.currentImageIndex = 0
      this.dialogVisible = true
    },
    /**
     * 关闭图片预览弹框
     * 关闭图片预览弹框并清理相关数据
     * @returns {void}
     */
    closeImagePreview() {
      this.dialogVisible = false
      this.previewImagesList = []
      this.currentImageIndex = 0
    },
    /**
     * 切换到上一张图片
     * 在图片预览模式下切换到前一张图片
     * @returns {void}
     */
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    /**
     * 切换到下一张图片
     * 在图片预览模式下切换到后一张图片
     * @returns {void}
     */
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    /**
     * 键盘事件处理器
     * 监听左右箭头键实现图片切换功能，ESC键关闭弹框
     * @param {KeyboardEvent} event - 键盘事件对象
     * @returns {void}
     */
    handleKeydown(event) {
      // 只在图片预览弹框打开时处理键盘事件
      if (!this.dialogVisible) return

      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'Escape') {
        this.closeImagePreview()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },

    /**
     * 重置任务表单
     * 清空任务表单的所有数据并重置相关组件状态
     * @returns {void}
     */
    resetAssignmentForm() {
      this.assignmentForm = {
        title: '',
        description: '',
        priority: '',
        category: '',
        comp_users: [],
        deadline: '',
        attachment_url: []
      }
      this.uploadFileList = []
      this.employeeOptions = []

      // 清理上传组件状态
      if (this.$refs.assignmentImageUploader) {
        this.$refs.assignmentImageUploader.clearFiles()
      }

      if (this.$refs.assignmentFormRef) {
        this.$refs.assignmentFormRef.clearValidate()
      }
    },

    /**
     * 处理粘贴的图片并自动上传
     * 将粘贴的图片文件上传到服务器
     * @param {Array<File>} files - 图片文件数组
     * @returns {void}
     */
    uploadPastedAssignmentImages(files) {
      // 检查当前已上传的图片数量
      const currentImageCount = this.assignmentForm.attachment_url.length

      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return
      }

      // 计算可以上传的图片数量
      const remainingSlots = 3 - currentImageCount
      const filesToUpload = files.slice(0, remainingSlots)

      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能上传${remainingSlots}张图片，已自动选择前${remainingSlots}张`)
      }

      filesToUpload.forEach(async (file, index) => {
        const tempFile = {
          name: file.name || `pasted-image-${Date.now()}-${index}.png`,
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0
        }

        this.$set(this.uploadFileList, this.uploadFileList.length, tempFile)

        try {
          const formData = new FormData()
          formData.append('file', file)

          const response = await this.$http.post(this.assignmentUploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.uploadFileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            this.handleAssignmentImageUploadSuccess(response.data, tempFile, this.uploadFileList)
          } else {
            // 上传失败，移除临时文件
            const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
            if (fileIndex !== -1) {
              this.uploadFileList.splice(fileIndex, 1)
            }
            this.$message.error('图片上传失败')
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          // 上传失败，移除临时文件
          const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.uploadFileList.splice(fileIndex, 1)
          }
          this.$message.error('图片上传失败')
        }
      })
    },

    /**
     * 处理图片列表变化事件
     * 当上传组件的文件列表发生变化时触发
     * @param {*} _ - 未使用的参数
     * @param {Array} fileList - 新的文件列表
     * @returns {void}
     */
    handAssignmentImageList(_, fileList) {
      this.uploadFileList = fileList
    },

    /**
     * 任务图片上传前校验
     * 检查图片格式、大小和数量限制
     * @param {File} file - 待上传的文件对象
     * @returns {boolean} 校验通过返回true，否则返回false
     */
    beforeAssignmentImageUpload(file) {
      // 检查图片数量限制
      const currentImageCount = this.assignmentForm.attachment_url.length
      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return false
      }

      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }

      return isValidType && isValidExtension && isLt5M
    },

    /**
     * 任务图片上传成功回调
     * 处理图片上传成功后的响应，更新文件列表和URL数组
     * @param {Object} response - 服务器响应对象
     * @param {Object} file - 上传的文件对象
     * @returns {void}
     */
    handleAssignmentImageUploadSuccess(response, file) {
      if (response.meta.status === 200) {
        this.assignmentForm.attachment_url.push(response.data.fileUrl)

        const index = this.uploadFileList.findIndex(f => f.uid === file.uid)
        if (index !== -1) {
          this.$set(this.uploadFileList, index, {
            ...this.uploadFileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100
          })
        }
      }
    },

    /**
     * 任务图片删除回调
     * 处理用户删除图片时的操作，同步更新相关数据
     * @param {Object} file - 被删除的文件对象
     * @returns {void}
     */
    handleAssignmentImageRemove(file) {
      const index = this.uploadFileList.indexOf(file)
      if (index > -1) {
        // 同步删除 attachment_url 中对应的 URL
        if (file.url && this.assignmentForm.attachment_url.includes(file.url)) {
          const urlIndex = this.assignmentForm.attachment_url.indexOf(file.url)
          this.assignmentForm.attachment_url.splice(urlIndex, 1)
        }
        // 删除 uploadFileList 中的文件
        this.uploadFileList.splice(index, 1)
      }
    },

    /**
     * 任务图片超出限制回调
     * 当用户尝试上传超过限制数量的图片时触发
     * @returns {void}
     */
    handleAssignmentImageExceed() {
      const currentCount = this.assignmentForm.attachment_url.length
      this.$message.warning(`已达到图片上传上限（3张），当前已有${currentCount}张图片`)
    },

    /**
     * 任务图片预览
     * 预览当前任务表单中的所有图片
     * @returns {void}
     */
    handleAssignmentImagePreview() {
      // 直接使用现有的图片预览功能
      this.previewImages(this.assignmentForm.attachment_url)
    },

    // ==================== 任务回复相关方法 ====================

    /**
     * 重置任务回复表单
     * 清空回复表单的所有数据并重置相关组件状态
     * @returns {void}
     */
    resetReplyForm() {
      this.replyForm = {
        reply_content: '',
        attachment_url: []
      }
      this.replyUploadFileList = []
    },

    /**
     * 处理任务回复描述粘贴事件
     * 支持从剪贴板粘贴图片并自动上传
     * @param {ClipboardEvent} event - 粘贴事件对象
     * @returns {Promise<void>} 无返回值，检测到图片时自动上传
     */
    async handleReplyDescriptionPaste(event) {
      const items = (event.clipboardData || event.originalEvent.clipboardData).items
      const imageFiles = []

      for (const item of items) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile()
          const isValid = this.beforeReplyImageUpload(file)
          if (isValid) {
            imageFiles.push(file)
          }
        }
      }

      if (imageFiles.length > 0) {
        event.preventDefault()
        this.uploadPastedReplyImages(imageFiles)
      }
    },

    /**
     * 处理粘贴的回复图片并自动上传
     * 将粘贴的图片文件上传到服务器
     * @param {Array<File>} files - 图片文件数组
     * @returns {void}
     */
    uploadPastedReplyImages(files) {
      // 检查当前已上传的图片数量
      const currentImageCount = this.replyForm.attachment_url.length

      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return
      }

      // 计算可以上传的图片数量
      const remainingSlots = 3 - currentImageCount
      const filesToUpload = files.slice(0, remainingSlots)

      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能上传${remainingSlots}张图片，已自动选择前${remainingSlots}张`)
      }

      filesToUpload.forEach(async (file, index) => {
        const tempFile = {
          name: file.name || `pasted-reply-image-${Date.now()}-${index}.png`,
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0
        }

        this.$set(this.replyUploadFileList, this.replyUploadFileList.length, tempFile)

        try {
          const formData = new FormData()
          formData.append('file', file)

          const response = await this.$http.post(this.replyUploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.replyUploadFileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.replyUploadFileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            this.handleReplyImageUploadSuccess(response.data, tempFile, this.replyUploadFileList)
          } else {
            // 上传失败，移除临时文件
            const fileIndex = this.replyUploadFileList.findIndex(f => f.uid === tempFile.uid)
            if (fileIndex !== -1) {
              this.replyUploadFileList.splice(fileIndex, 1)
            }
            this.$message.error('图片上传失败')
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          // 上传失败，移除临时文件
          const fileIndex = this.replyUploadFileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.replyUploadFileList.splice(fileIndex, 1)
          }
          this.$message.error('图片上传失败')
        }
      })
    },

    /**
     * 处理回复图片列表变化事件
     * 当上传组件的文件列表发生变化时触发
     * @param {*} _ - 未使用的参数
     * @param {Array} fileList - 新的文件列表
     * @returns {void}
     */
    handleReplyImageList(_, fileList) {
      // 更新文件列表
      this.replyUploadFileList = fileList

      // 额外的数据同步检查：确保 attachment_url 与成功上传的文件保持一致
      const successfulUrls = fileList
        .filter(file => file.status === 'success' && file.url)
        .map(file => file.url)

      // 如果发现不一致，进行同步（这是一个保护措施）
      if (successfulUrls.length !== this.replyForm.attachment_url.length) {
        this.replyForm.attachment_url = successfulUrls
      }
    },

    /**
     * 回复图片上传前校验
     * 检查图片格式、大小和数量限制
     * @param {File} file - 待上传的文件对象
     * @returns {boolean} 校验通过返回true，否则返回false
     */
    beforeReplyImageUpload(file) {
      // 检查图片数量限制
      const currentImageCount = this.replyForm.attachment_url.length
      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return false
      }

      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }

      return isValidType && isValidExtension && isLt5M
    },

    /**
     * 回复图片上传成功回调
     * 处理图片上传成功后的响应，更新文件列表和URL数组
     * @param {Object} response - 服务器响应对象
     * @param {Object} file - 上传的文件对象
     * @returns {void}
     */
    handleReplyImageUploadSuccess(response, file) {
      if (response.meta.status === 200) {
        this.replyForm.attachment_url.push(response.data.fileUrl)

        const index = this.replyUploadFileList.findIndex(f => f.uid === file.uid)
        if (index !== -1) {
          this.$set(this.replyUploadFileList, index, {
            ...this.replyUploadFileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100
          })
        }
      }
    },

    /**
     * 回复图片删除回调
     * 处理用户删除图片时的操作，同步更新相关数据
     * @param {Object} file - 被删除的文件对象
     * @returns {void}
     */
    handleReplyImageRemove(file) {
      const index = this.replyUploadFileList.indexOf(file)

      if (index > -1) {
        // 获取要删除的文件URL，支持多种情况
        let fileUrl = null
        let urlSource = ''

        // 情况1：file.url 存在（正常上传成功的文件）
        if (file.url) {
          fileUrl = file.url
          urlSource = 'file.url'
        } else if (file.response && file.response.data && file.response.data.fileUrl) {
          // 情况2：file.response 存在（刚上传成功的文件）
          fileUrl = file.response.data.fileUrl
          urlSource = 'file.response.data.fileUrl'
        }

        // 从 attachment_url 中删除对应的 URL
        let urlDeleted = false
        if (fileUrl) {
          const urlIndex = this.replyForm.attachment_url.indexOf(fileUrl)

          if (urlIndex !== -1) {
            this.replyForm.attachment_url.splice(urlIndex, 1)
            urlDeleted = true
          } else {
            console.warn('- 警告：在 replyForm.attachment_url 中未找到匹配的URL')
          }
        }

        // 备用方案：如果无法通过URL删除，通过索引位置删除
        if (!urlDeleted) {
          console.warn('- 启用备用删除方案：通过索引位置删除')
          if (index < this.replyForm.attachment_url.length) {
            this.replyForm.attachment_url.splice(index, 1)
            urlDeleted = true
          } else {
            console.error('- 错误：索引超出 replyForm.attachment_url 范围')
          }
        }

        // 删除 replyUploadFileList 中的文件
        this.replyUploadFileList.splice(index, 1)
      }
    },

    /**
     * 回复图片超出限制回调
     * 当用户尝试上传超过限制数量的图片时触发
     * @returns {void}
     */
    handleReplyImageExceed() {
      const currentCount = this.replyForm.attachment_url.length
      this.$message.warning(`已达到图片上传上限（3张），当前已有${currentCount}张图片`)
    },

    /**
     * 回复图片预览
     * 预览当前回复表单中的所有图片
     * @returns {void}
     */
    handleReplyImagePreview() {
      // 直接使用现有的图片预览功能
      this.previewImages(this.replyForm.attachment_url)
    },

    /**
     * 验证任务回复表单
     * 检查任务回复内容是否为空
     * @returns {boolean} 验证通过返回true，否则返回false
     */
    validateReplyForm() {
      if (!this.replyForm.reply_content || this.replyForm.reply_content.trim() === '') {
        this.$message.error('请输入任务回复内容')
        return false
      }
      return true
    },

    /**
     * 完成任务
     * 验证表单并显示二次确认对话框，确认后调用完成任务接口
     * @returns {Promise<void>} 无返回值
     */
    async completeTask() {
      // 验证表单
      if (!this.validateReplyForm()) {
        return
      }

      // 二次确认
      try {
        await this.$confirm('该条任务确认是否完成，点击后状态不可再次变更', '确认完成任务', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用完成任务接口
        await this.submitTaskReply(2)
      } catch (error) {
        // 用户取消操作
        if (error !== 'cancel') {
          console.error('完成任务失败:', error)
        }
      }
    },

    /**
     * 保存任务进度
     * 保存当前任务回复内容，不改变任务状态
     * @returns {Promise<void>} 无返回值
     */
    async saveTaskProgress() {
      // 调用保存接口
      await this.submitTaskReply(1)
    },

    /**
     * 提交任务回复
     * 调用后端API提交任务回复数据
     * @param {number} type - 操作类型：1=保存进度，2=完成任务
     * @returns {Promise<void>} 无返回值
     */
    async submitTaskReply(type) {
      if (!this.replyForm.reply_content) { return this.$message.warning('请输入回复内容') }
      try {
        const requestData = {
          id: this.assignmentDetail.id,
          type: type,
          reply_content: this.replyForm.reply_content,
          attachment_url: this.replyForm.attachment_url
        }

        const { data: res } = await this.$http.post(`${this.model}/complete_assignment/`, requestData)
        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '操作失败')
        }

        // 操作成功
        const operationText = type === 2 ? '完成任务' : '保存进度'
        this.$message.success(`${operationText}成功`)

        // 如果是完成任务，更新任务状态并禁用按钮
        if (type === 2) {
          this.assignmentDetail.status = 1 // 设置为已完成状态
        }

        // 重新获取任务列表以更新数据
        await this.getAssignmentList()
        this.showAssignmentDetailDialog = false
      } catch (error) {
        console.error('提交任务回复失败:', error)
        const operationText = type === 2 ? '完成任务' : '保存进度'
        this.$message.error(`${operationText}失败，请稍后重试`)
      }
    }
  }
}
</script>

<style scoped>
.cell-content {
  cursor: pointer;
}

.cell-content:hover {
  text-decoration: underline;
}

/* 图片预览弹框样式 */
.transparent-dialog {
  background-color: rgba(255, 255, 255, 0.13);
  box-shadow: none;
  margin: 0;
  position: fixed;
  top: 0;
}

.dark-popover .el-popover__title {
  color: #FFFFFF;
}

.dark-popover .popper__arrow {
  border-top-color: #303133;
  border-bottom-color: #303133;
}

.dark-popover .popper__arrow::after {
  border-top-color: #303133;
  border-bottom-color: #303133;
}

/* 查询表单样式 */
.query-form .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.query-form .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: normal;
  padding-right: 8px;
}

.query-form .el-form-item__content {
  margin-left: 0 !important;
}

/* 任务详情弹框样式 */
.assignment-detail-header {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

/* 字段行对齐样式 */
.field-row-item {
  display: flex;
  align-items: center;
  min-height: 32px;
  gap: 8px;
}

.field-row-item label {
  margin: 0;
  flex-shrink: 0;
  line-height: 32px;
}

.field-text-content {
  line-height: 32px;
  display: inline-flex;
  align-items: center;
}

.field-button-content {
  height: 32px;
  line-height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
}

/* 确保 el-tag 在字段行中的对齐 */
.field-row-item .el-tag {
  height: 24px;
  line-height: 22px;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

</style>
