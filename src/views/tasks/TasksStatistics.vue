<template>
  <div>
    <!-- 任务报表统计 -->
    <el-card>
      <el-row>
        <div class="title" style="text-align: center; font-weight: bold;">任务处理及时率统计
          <el-tooltip placement="top">
            <div slot="content">
              <p>1、首次进入默认显示当前登录人所有已发布任务中处理人的单个任务平均处理时长</p>
              <p>2、手动查询必须输入任务名称，可查询单个任务的处理时长和延期时长</p>
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-row>
      <el-row>
        <div style="margin:30px auto; width: fit-content;">
          <el-form :model="queryForm" :rules="rules" ref="queryForm" inline>
            <!-- 任务名称输入框（必填） -->
            <!--<el-form-item prop="title" label="任务名称">-->
            <!--<el-input v-model="queryForm.title" placeholder="请输入任务名称"/>-->
            <!--</el-form-item>-->
            <el-form-item prop="title" label="任务名称">
              <el-select
                v-model="queryForm.title"
                filterable
                clearable
                remote
                :remote-method="searchAssignment"
                :loading="searchLoading"
                placeholder="请输入任务名称关键词搜索"
                style="width: 100%"
                @change="handleAssignmentChange"
              >
                <el-option
                  v-for="item in assignmentList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.title"
                />
              </el-select>
            </el-form-item>
            <!-- 其他查询条件 -->
            <el-form-item label="处理人">
              <el-input v-model="queryForm.comp_user" placeholder="处理人名称"/>
            </el-form-item>
            <el-form-item label="时间类型">
              <el-select v-model="queryForm.time_type" placeholder="请选择">
                <el-option label="处理时长" value="1"/>
                <el-option label="延期时长" value="2"/>
              </el-select>
            </el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </el-form>
        </div>
      </el-row>
      <el-row>
        <el-col>
          <div v-if="isTaskTimeStatisticsCharts" style="width:100%;height:400px;margin-top:30px">
            <el-empty :image-size="200"></el-empty>
          </div>
          <div v-show="!isTaskTimeStatisticsCharts" id="taskTimeStatisticsCharts" :style="{ width: '100%', height: chartHeight + 'px', marginTop: '30px' }"></div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      assignmentList: [],
      searchLoading: false, // 加载状态
      searchTimer: null, // 防抖定时器
      options: [{
        value: '1',
        label: '处理时长'
      }, {
        value: '2',
        label: '延期时长'
      }],
      queryInfo: { // 查询栏
        title: '', // 任务标题
        description: '', // 任务描述
        priority: '', // 优先级
        status: '', // 任务状态
        category: '', // 任务类型
        start_date: '', // 发布时间-开始时间
        end_date: '', // 发布时间-结束时间
        deadline_start: '', // 截止时间-开始时间
        deadline_end: '', // 截止时间-结束时间
        pagenum: 1,
        pagesize: 200
      },
      user_name: window.localStorage.getItem('user_name'),
      user_account: window.localStorage.getItem('user_account'),
      queryForm: {
        comp_user: '',
        title: '',
        time_type: '1',
        send_from: ''
      },
      model: 'assignment',
      isTaskTimeStatisticsCharts: false,
      chartHeight: 400, // 初始高度
      // 对查询条件做校验
      rules: {
        title: [
          // 动态规则：初始不校验，点击查询时通过 validateField 触发校验
          {
            validator: (rule, value, callback) => {
              if (this.isQueryClicked && !value.trim()) {
                callback(new Error('任务名称不能为空！'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      isQueryClicked: false, // 默认不对查询条件做校验
      chartInstance: null
    }
  },
  activated() {
    if (this.chartInstance) {
      this.chartInstance.resize()
    }
  },
  created() {
    this.getTaskTimeStatisticsCharts()
    // this.getAssignmentList()
  },
  methods: {
    handleQuery() {
      // 启用校验
      this.isQueryClicked = true
      this.$refs.queryForm.validate((valid) => {
        if (valid) {
          // 校验通过，执行查询
          this.getTaskTimeStatisticsCharts()
        } else {
          // 校验失败，自动提示错误（Element UI 内部处理）
          return false
        }
      })
    },
    // 获取任务列表
    async getAssignmentList(query) {
      try {
        this.queryInfo.pagefrom = 'publish_tasks'
        this.queryInfo.title = query
        const { data: res } = await this.$http.post(this.model + '/assignment_list/', this.queryInfo)
        if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
        this.total = res.data.total
        this.assignmentList = res.data.assignments.map(item => ({
          ...item
        }))
      } catch (error) {
        console.warn('API调用失败:', error)
      }
    },
    searchAssignment(query) {
      if (this.searchTimer) clearTimeout(this.searchTimer)

      if (query && query.length >= 2) { // 至少2个字符才搜索
        this.searchTimer = setTimeout(() => {
          this.searchLoading = true
          this.getAssignmentList(query).finally(() => {
            this.searchLoading = false
          })
        }, 500) // 500ms防抖
      } else {
        this.assignmentList = []
      }
    },
    handleAssignmentChange(selectedTitle) {
      console.log('选择了任务:', selectedTitle)
    },
    async getTaskTimeStatisticsCharts() {
      this.queryForm.send_from = this.user_account
      const { data: res } = await this.$http.get(this.model + '/get_task_time_statistics/', { params: this.queryForm })
      // 动态计算容器高度：每个处理人占70px
      if (res.data.length < 4) {
        this.chartHeight = 300
      } else {
        this.chartHeight = res.data.length * 70
      }
      await this.$nextTick() // 确保DOM更新
      const chartDom = document.getElementById('taskTimeStatisticsCharts')
      let chartInstance = this.$echarts.getInstanceByDom(chartDom)
      if (!chartInstance) {
        chartInstance = this.$echarts.init(chartDom)
      }
      // 提取所有处理人作为  轴数据
      const yAxisData = res.data.map(item => item.comp_name)
      if (
        !yAxisData.length
      ) {
        chartInstance.clear() // 清除旧图表
        // 获取图表的容器元素
        this.isTaskTimeStatisticsCharts = true
        return
      }
      this.isTaskTimeStatisticsCharts = false
      // 根据 time_type 动态设置数据字段和图表标题
      const isProcessingTime = this.queryForm.time_type === '1'
      const dataField = isProcessingTime ? 'avg_time' : 'delay_time'
      const timeWithUnitField = isProcessingTime ? 'avg_time_with_unit' : 'delay_time_with_unit'
      const chartTitle = isProcessingTime ? '平均处理时长' : '平均延期时长'
      const tooltipSuffix = isProcessingTime ? '处理时长' : '延期时长'
      const barColor = isProcessingTime ? '#5470C6' : '#EE6666'
      // 提取平均处理/延期时长作为 x 轴数据
      const seriesData = res.data.map(item => item[dataField])
      // 配置 ECharts 选项
      const option = {
        tooltip: {
          trigger: 'axis', // 触发方式为坐标轴
          formatter: function (params) {
            // 使用带单位的时间格式
            const timeWithUnit = res.data.find(item => item.comp_name === params[0].name)[timeWithUnitField]
            return `处理人: ${params[0].name}<br><span class='dot'></span>${tooltipSuffix}: ${timeWithUnit}`
          }
        },
        legend: {
          data: [chartTitle], // 图例名称要与 series 里的 name 对应
          top: 'top', // 图例位置，可根据需求调整
          textStyle: {
            color: '#333' // 图例文字颜色
          }
        },
        xAxis: {
          type: 'value',
          // name: '时长（分钟）',
          axisLabel: {
            show: false // 不显示刻度
          },
          axisLine: {
            show: true, // 显示轴线
            lineStyle: {
              color: '#000', // 黑色轴线
              width: 1 // 线宽
            }
          }
        },
        min: 'datamin',
        max: function (value) {
          return value.max * 1.1
        },
        yAxis: {
          type: 'category', // 类目轴
          data: yAxisData, // 处理人姓名
          inverse: true
        },
        series: [
          {
            name: chartTitle,
            type: 'bar', // 柱状图
            barWidth: 30, // 设置柱状图的宽度
            data: res.data.map(item => ({
              value: item[dataField],
              // 添加带单位的时间作为label
              label: {
                formatter: item[timeWithUnitField]
              }
            })), // 平均处理时长数据
            itemStyle: {
              color: barColor // 柱状颜色
            },
            label: {
              show: true, // 显示数值
              position: 'right' // 位置（可选：inside, outside, top, bottom, left, right）
            }
          }
        ],
        grid: {
          left: '2%', // 调整左侧留白
          right: '13%', // 调整右侧留白
          bottom: '3%', // 调整底部留白
          containLabel: true // 包含 y 轴标签
        }
      }
      chartInstance.clear()
      chartInstance.setOption(option, true)
      this.$nextTick(() => {
        chartInstance.resize()
      })
      // 窗口缩放监听
      window.addEventListener('resize', () => chartInstance.resize())
      this.chartInstance = chartInstance
    }
  }
}
</script>
