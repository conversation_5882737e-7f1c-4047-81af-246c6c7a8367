<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>任务管理</el-breadcrumb-item>
      <el-breadcrumb-item>发布任务</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <!-- 查询表单第一行 -->
      <el-form :inline="true" :model="queryInfo" class="query-form">
        <el-row :gutter="15">
          <el-col :span="5">
            <el-input placeholder="任务名称"
                      v-model="queryInfo.title"
                      clearable
                      @keyup.enter.native="queryList">
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="任务描述"
                      v-model="queryInfo.description"
                      clearable
                      @keyup.enter.native="queryList">
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select placeholder="优先级"
                       v-model="queryInfo.priority"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="紧急"></el-option>
              <el-option value="1" label="高"></el-option>
              <el-option value="2" label="中"></el-option>
              <el-option value="3" label="低"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select placeholder="任务状态"
                       v-model="queryInfo.status"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="进行中"></el-option>
              <el-option value="1" label="已完成"></el-option>
              <el-option value="2" label="已关闭"></el-option>
              <el-option value="3" label="已延期"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select placeholder="任务类型"
                       v-model="queryInfo.category"
                       clearable
                       style="width: 100%">
              <el-option value="0" label="客户经营"></el-option>
              <el-option value="1" label="工单处理"></el-option>
              <el-option value="2" label="款项跟进"></el-option>
              <el-option value="3" label="公司事项"></el-option>
              <el-option value="4" label="其他"></el-option>
            </el-select>
          </el-col>
        </el-row>

        <!-- 操作按钮行 -->
        <el-row :gutter="5" style="margin-top: 10px;">
          <el-col :span="6">
            <el-form-item label="创建时间" style="margin-bottom: 0;">
              <el-date-picker
                v-model="queryCreateDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
                style="width: 225px;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="截止时间" style="margin-bottom: 0;">
              <el-date-picker
                v-model="queryDeadlineDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDeadlineDateChange"
                style="width: 225px;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-input placeholder="发布人名称或工号"
                      v-model="queryInfo.send_from"
                      clearable
                      @keyup.enter.native="queryList"
                      v-if="is_white_user">
            </el-input>
          </el-col>
          <el-col :span="7" style="display: flex; align-items: center; justify-content: flex-end;">
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button type="primary" @click="queryReset">重置</el-button>
            <el-button type="primary" @click="queryExport">导出</el-button>
            <el-button type="warning" @click="publishAssignment" v-if="can_add">新增任务</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 任务列表表格 -->
      <el-row>
        <el-table
          :data="assignmentList"
          stripe
          border
          :header-cell-style="{
            backgroundColor: '#e6f7ff',
            color: '#606266',
            fontSize: '12px',
            fontWeight: 'bold'
          }">

          <el-table-column label="任务名称" prop="title" min-width="150px" align="center"></el-table-column>
          <el-table-column min-width="200px">
            <template #header>
              <span>
                任务描述
                <el-tooltip content="点击单元格可查看任务描述详情" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
                </el-tooltip>
              </span>
            </template>
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover">
                <div style="white-space: pre-line; font-size: 12px">{{ scope.row.description }}</div>
                <div slot="reference"
                     class="cell-content"
                     style="cursor: pointer;">
                  <div style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                    {{ scope.row.description }}
                  </div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="任务类型" min-width="90px" align="center">
            <template #default="scope">
              {{ getAssignmentTypeLabel(scope.row.category) }}
            </template>
          </el-table-column>
          <el-table-column label="优先级" min-width="70px" align="center">
            <template #default="scope">
              <el-tag :type="getPriorityTagType(scope.row.priority)">
                {{ getPriorityLabel(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="图片" min-width="90px" align="center">
            <template #default="scope">
              <div v-if="scope.row.attachment_url && scope.row.attachment_url.length > 0">
                <el-link @click="previewImages(scope.row.attachment_url)" style="font-weight: normal; font-size: x-small">
                  查看图片({{ scope.row.attachment_url.length }})
                </el-link>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column min-width="100px" align="center">
            <template #header>
              <span>
                任务进度
                <el-tooltip content="已完成任务人数/任务总人数" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
                </el-tooltip>
              </span>
            </template>
            <template #default="scope">
              {{ scope.row.completed_count }}/{{ scope.row.total_count }}
            </template>
          </el-table-column>
          <el-table-column label="任务状态" min-width="80px" align="center">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发布人"  v-if="is_white_user" show-overflow-tooltip prop="send_from" min-width="100px" align="center">
            <template #default="scope">
              {{scope.row.send_from_name}}({{ scope.row.send_from }})
            </template>
          </el-table-column>
          <el-table-column label="任务创建时间" show-overflow-tooltip prop="create_time" min-width="150px" align="center"></el-table-column>
          <el-table-column label="任务截止时间" show-overflow-tooltip prop="deadline" min-width="150px" align="center"></el-table-column>
          <el-table-column label="操作" min-width="125px" align="center">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="编辑任务" placement="top" :enterable="false">
                <el-button circle type="primary" icon="el-icon-edit" size="mini" @click="viewAssignmentDetail(slotProps.row)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="关闭任务" placement="top" :enterable="false" v-if="slotProps.row.send_from === staff_no">
                <el-popconfirm
                  title="请确认关闭该任务？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="closeAssignment(slotProps.row)"
                  style="margin-left: 10px;"
                >
                  <el-button :disabled="slotProps.row.status === 2 || slotProps.row.status === 1"
                             icon="el-icon-switch-button"
                             circle
                             type="info"
                             size="mini"
                             slot="reference"></el-button>
                </el-popconfirm>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除任务" placement="top" :enterable="false" v-if="slotProps.row.send_from === staff_no">
                <el-popconfirm
                  title="请确认删除该任务？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="deleteAssignment(slotProps.row)"
                  style="margin-left: 10px;">
                  <el-button
                    icon="el-icon-delete"
                    circle
                    type="warning"
                    size="mini"
                    slot="reference"
                    :disabled="slotProps.row.status === 2 || slotProps.row.status === 1">
                  </el-button>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <!-- 分页 -->
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>

      <!-- 图片预览弹框 -->
      <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 自定义关闭按钮 -->
          <div @click="closeImagePreview"
               style="position: absolute; top: 20px; right: 20px; cursor: pointer; color: white; font-size: 24px; background: rgba(0,0,0,0.6); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; z-index: 1000; transition: background-color 0.3s;"
               @mouseenter="$event.target.style.backgroundColor = 'rgba(0,0,0,0.8)'"
               @mouseleave="$event.target.style.backgroundColor = 'rgba(0,0,0,0.6)'">
            &#10005;
          </div>
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;">
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 新增任务弹框 -->
      <el-dialog
        title="新增任务"
        :visible.sync="showAddAssignmentDialog"
        width="800px"
        :close-on-click-modal="false"
        @close="handleAddAssignmentDialogClose">
        <el-form
          :model="assignmentForm"
          :rules="assignmentFormRules"
          ref="assignmentFormRef"
          label-width="100px">

          <!-- 任务名称 -->
          <el-form-item label="任务名称" prop="title">
            <el-input
              v-model="assignmentForm.title"
              placeholder="请输入任务名称"
              maxlength="100"
              show-word-limit
              clearable
              class="unified-placeholder">
            </el-input>
          </el-form-item>

          <!-- 任务描述 -->
          <el-form-item label="任务描述" prop="description">
            <el-input
              type="textarea"
              v-model="assignmentForm.description"
              placeholder="请输入任务描述"
              :rows="4"
              maxlength="2000"
              show-word-limit
              @paste.native="handleAssignmentDescriptionPaste"
              ref="assignmentDescriptionInput"
              class="unified-placeholder">
            </el-input>
          </el-form-item>

          <!-- 优先级和任务类型 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="优先级" prop="priority">
                <el-select v-model="assignmentForm.priority" placeholder="请选择优先级" style="width: 100%">
                  <el-option value="0" label="紧急"></el-option>
                  <el-option value="1" label="高"></el-option>
                  <el-option value="2" label="中"></el-option>
                  <el-option value="3" label="低"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务类型" prop="category">
                <el-select v-model="assignmentForm.category" placeholder="请选择任务类型" style="width: 100%">
                  <el-option value="0" label="客户经营"></el-option>
                  <el-option value="1" label="工单处理"></el-option>
                  <el-option value="2" label="款项跟进"></el-option>
                  <el-option value="3" label="公司事项"></el-option>
                  <el-option value="4" label="其他"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 处理人 -->
          <el-form-item label="处理人" prop="comp_users">
            <div class="assignee-input-wrapper">
              <el-button
                plain
                @click="openEmployeeSelectDialog"
                :class="[
                  'assignee-select-button',
                  { 'limit-reached': assignmentForm.comp_users.length >= maxPersonLimit }
                ]">
                已选择 {{ assignmentForm.comp_users.length }}/{{ maxPersonLimit }} 人
                <span v-if="assignmentForm.comp_users.length >= maxPersonLimit" style="color: #F56C6C; margin-left: 8px;">
                  （已达到最大限制）
                </span>
              </el-button>
              <i
                v-if="assignmentForm.comp_users.length > 0"
                class="el-icon-circle-close assignee-clear-icon"
                @click="clearSelectedEmployees"
                title="清空所有已选择的处理人">
              </i>
            </div>
          </el-form-item>

          <!-- 截止时间 -->
          <el-form-item label="截止时间" prop="deadline">
            <el-date-picker
              v-model="assignmentForm.deadline"
              type="datetime"
              placeholder="请选择截止时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="deadlinePickerOptions"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>

          <!-- 上传图片 -->
          <el-form-item label="上传图片">
            <el-upload
              ref="assignmentImageUploader"
              list-type="picture-card"
              :action="assignmentUploadPath"
              :multiple="true"
              :limit="3"
              :file-list="uploadFileList"
              :on-preview="handleAssignmentImagePreview"
              :on-remove="handleAssignmentImageRemove"
              :on-success="handleAssignmentImageUploadSuccess"
              :on-exceed="handleAssignmentImageExceed"
              :before-upload="beforeAssignmentImageUpload"
              :on-change="handAssignmentImageList"
              :auto-upload="true">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                支持jpg/png格式，单张图片不超过5MB，最多3张图片<br>
                支持在任务描述中直接粘贴图片（Ctrl+V）<br>
                <span :style="{ color: assignmentForm.attachment_url.length >= 3 ? '#F56C6C' : '#909399' }">
                  当前已上传：{{ assignmentForm.attachment_url.length }}/3 张图片
                </span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="showAddAssignmentDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitAssignmentForm">发布任务</el-button>
        </span>
      </el-dialog>

      <!-- 员工选择弹框 -->
      <el-dialog
        title="选择处理人"
        :visible.sync="showEmployeeSelectDialog"
        width="900px"
        :close-on-click-modal="false"
        @close="handleEmployeeSelectDialogClose">

        <!-- 筛选条件 -->
        <div class="employee-filter-section" style="margin-bottom: 20px;">
          <el-row :gutter="15">
            <el-col :span="6">
              <el-input
                placeholder="工号"
                v-model="employeeFilter.emp_account"
                clearable
                @input="handleEmployeeFilter">
                <i slot="prefix" class="el-icon-search el-input__icon"></i>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                placeholder="员工姓名"
                v-model="employeeFilter.emp_name"
                clearable
                @input="handleEmployeeFilter">
                <i slot="prefix" class="el-icon-search el-input__icon"></i>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                placeholder="所属部门"
                v-model="employeeFilter.dept_name"
                clearable
                @input="handleEmployeeFilter">
                <i slot="prefix" class="el-icon-search el-input__icon"></i>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="searchEmployeeList">查询</el-button>
              <el-button @click="resetEmployeeFilter">重置</el-button>
            </el-col>
          </el-row>

          <!-- 查询提示 -->
          <el-row style="margin-top: 15px;">
            <el-col :span="24">
              <el-alert
                description="默认展示已选员工，请至少填写工号、员工姓名、所属部门中的一个条件，然后点击查询按钮来搜索员工。"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </el-col>
          </el-row>

          <!-- 选择状态提示 -->
          <el-row style="margin-top: 15px;" v-if="selectedUsers.length >= maxPersonLimit">
            <el-col :span="24">
              <el-alert
                title="已达到最大选择人数限制"
                :description="`当前已选择 ${selectedUsers.length}/${maxPersonLimit} 人，如需选择其他人员，请先取消勾选部分已选人员。`"
                type="warning"
                :closable="false"
                show-icon>
              </el-alert>
            </el-col>
          </el-row>
        </div>

        <!-- 员工列表表格 -->
        <el-table
          ref="employeeTable"
          :data="employeeList"
          stripe
          border
          height="400"
          @selection-change="handleEmployeeSelectionChange"
          :header-cell-style="{
            backgroundColor: '#e6f7ff',
            color: '#606266',
            fontSize: '12px',
            fontWeight: 'bold'
          }">

          <el-table-column
            type="selection"
            width="55"
            align="center"
            :selectable="checkEmployeeSelectable">
          </el-table-column>

          <el-table-column label="员工姓名" prop="emp_name" min-width="100px" align="center"></el-table-column>
          <el-table-column label="工号" prop="emp_account" min-width="100px" align="center"></el-table-column>
          <el-table-column label="职位" prop="emp_position" min-width="120px" align="center"></el-table-column>
          <el-table-column label="所属部门" prop="dept_info" min-width="300px" align="center">
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="formatDeptInfo(scope.row.dept_info)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 180px; cursor: pointer;">
                  {{ scope.row.dept_info }}
                </div>
              </el-popover>
            </template>
<!--            <template #default="scope">-->
<!--              <el-tooltip-->
<!--                :content="formatDeptInfo(scope.row.dept_info)"-->
<!--                placement="top"-->
<!--                :enterable="true"-->
<!--                :hide-after="0"-->
<!--                popper-class="dept-tooltip">-->
<!--                <div class="dept-cell">-->
<!--                  {{ scope.row.dept_info }}-->
<!--                </div>-->
<!--              </el-tooltip>-->
<!--            </template>-->
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-row style="margin-top: 20px;">
          <el-pagination
            @size-change="handleEmployeeSizeChange"
            @current-change="handleEmployeeCurrentChange"
            :current-page.sync="employeeQueryInfo.pagenum"
            :page-size="employeeQueryInfo.pagesize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="employeeTotal">
          </el-pagination>
        </el-row>

        <span slot="footer" class="dialog-footer">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 14px;" :style="{ color: selectedUsers.length >= maxPersonLimit ? '#F56C6C' : '#606266' }">
              已选择 {{ this.selectedUsers.length }}/{{ maxPersonLimit }} 人
              <span v-if="selectedUsers.length >= maxPersonLimit" style="color: #F56C6C; margin-left: 8px;">
                （已达到最大限制）
              </span>
            </span>
            <div>
              <el-button @click="showEmployeeSelectDialog = false">取 消</el-button>
              <el-button type="primary" @click="confirmEmployeeSelection">确 定</el-button>
            </div>
          </div>
        </span>
      </el-dialog>

      <!-- 二次确认弹框 -->
      <el-dialog
        title="确认发布任务"
        :visible.sync="showConfirmDialog"
        width="400px"
        :close-on-click-modal="false">
        <div style="text-align: center; padding: 20px 0;">
          <i class="el-icon-warning" style="font-size: 48px; color: #E6A23C; margin-bottom: 20px;"></i>
          <p style="font-size: 15px; margin-bottom: 20px;">
            该条任务将下发至 <span style="color: #E6A23C; font-weight: bold;">{{ assignmentForm.comp_users.length }}</span> 人，请确认是否下发？
          </p>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showConfirmDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmPublishAssignment">确认下发</el-button>
        </span>
      </el-dialog>

      <!-- 任务详情弹框 -->
      <el-dialog
        title="任务详情"
        :visible.sync="showAssignmentDetailDialog"
        width="1200px"
        :close-on-click-modal="false"
        @close="handleAssignmentDetailDialogClose">
        <!-- 任务基本信息 -->
        <div v-if="assignmentDetail" class="assignment-detail-header">
          <el-row :gutter="20" style="margin-top: 10px; margin-left: 30px">
            <el-col :span="12">
              <div class="info-item">
                <label style="font-size: 15px">任务下发时间：</label>
                <span>{{ assignmentDetail.create_time }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label style="font-size: 15px">任务截止时间：</label>
                <span>{{ assignmentDetail.deadline }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-left: 30px">
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">优先级：</label>
                <el-tag :type="getPriorityTagType(assignmentDetail.priority)">
                  {{ getPriorityLabel(assignmentDetail.priority) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">任务类型：</label>
                <span class="field-text-content">{{ getAssignmentTypeLabel(assignmentDetail.category) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item field-row-item">
                <label style="font-size: 15px">任务图片：</label>
                <div v-if="assignmentDetail.attachment_url && assignmentDetail.attachment_url.length > 0">
                  <el-button type="text" @click="previewImages(assignmentDetail.attachment_url)" class="field-button-content">
                    查看图片({{ assignmentDetail.attachment_url.length }})
                  </el-button>
                </div>
                <span v-else class="field-text-content">无</span>
              </div>
            </el-col>
          </el-row>

          <el-row  :gutter="20" style="margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <label style="font-size: 15px">任务名称：</label>
                <span style="font-weight: bold;">{{ assignmentDetail.title }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row  :gutter="20" style="margin-left: 30px">
            <el-col :span="24">
              <div class="info-item">
                <label style="font-size: 15px">任务描述：</label>
                <div style="white-space: pre-line; margin-top: 5px; padding: 10px; background-color: #f5f7fa; border-radius: 4px;">
                  {{ assignmentDetail.description }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 处理人搜索和筛选 -->
        <div class="handler-search-section" style="margin-bottom: 20px;">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input
                placeholder="搜索处理人姓名或ID"
                v-model="handlerSearchQuery"
                clearable
                @input="filterHandlers">
                <i slot="prefix" class="el-icon-search el-input__icon"></i>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select
                placeholder="状态筛选"
                v-model="handlerStatusFilter"
                clearable
                @change="filterHandlers">
                <el-option :value="0" label="进行中"></el-option>
                <el-option :value="1" label="已完成"></el-option>
                <el-option :value="2" label="已关闭"></el-option>
                <el-option :value="3" label="已延期"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" :disabled="selectedHandlers.length === 0 || isUrgeDisabled" @click="batchUrge">
                批量催促 ({{ selectedHandlers.length }})
              </el-button>
              <el-button type="danger" :disabled="selectedHandlers.length === 0" @click="batchRemove"  v-if="assignmentDetail && assignmentDetail.send_from === staff_no">
                批量移除 ({{ selectedHandlers.length }})
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 处理人详情表格 - 使用虚拟滚动优化 -->
        <div class="virtual-table-container">
          <!-- 表格头部 -->
          <div class="virtual-table-header">
            <div class="virtual-table-row header-row">
              <div class="virtual-table-cell selection-cell">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  @change="handleCheckAllChange">
                </el-checkbox>
              </div>
              <div class="virtual-table-cell handler-cell">处理人</div>
              <div class="virtual-table-cell status-cell">状态</div>
              <div class="virtual-table-cell reply-cell">任务回复</div>
              <div class="virtual-table-cell image-cell">回复图片</div>
              <div class="virtual-table-cell time-cell">处理时长</div>
              <div class="virtual-table-cell delay-status-cell">是否延期</div>
              <div class="virtual-table-cell delay-time-cell">延期时长</div>
            </div>
          </div>

          <!-- 虚拟滚动内容区域 -->
          <RecycleScroller
            ref="handlerScroller"
            class="virtual-table-body"
            :style="{ height: dynamicTableHeight - 60 + 'px' }"
            :items="filteredHandlers"
            :item-size="estimatedItemSize"
            key-field="comp_user"
            v-slot="{ item, index }"
            :buffer="200"
            :prerender="10">
            <div
              class="virtual-table-row data-row"
              :class="{ 'stripe-row': index % 2 === 1 }">
              <div class="virtual-table-cell selection-cell">
                <el-checkbox
                  :value="selectedHandlerIds.includes(item.comp_user)"
                  @change="(checked) => handleSingleHandlerCheck(item, checked)"
                  :disabled="!isHandlerSelectable(item)">
                </el-checkbox>
              </div>
              <div class="virtual-table-cell handler-cell">
                {{ item.comp_user_name }}({{ item.comp_user }})
              </div>
              <div class="virtual-table-cell status-cell">
                <el-tag :type="getHandlerStatusTagType(item.status)">
                  {{ getHandlerStatusLabel(item.status) }}
                </el-tag>
              </div>
              <div class="virtual-table-cell reply-cell">
                <div v-if="item.reply_content">
                  <template>
                    <el-popover
                      placement="top"
                      width="800"
                      trigger="click"
                      popper-class="dark-popover reply-popover"
                      :offset="20">
                      <div style="white-space: pre-line; font-size: 12px">{{ item.reply_content }}</div>
                      <div slot="reference"
                           class="cell-content reply-text-container"
                           style="cursor: pointer;">
                        <div class="reply-text-ellipsis">
                          {{ item.reply_content }}
                        </div>
                      </div>
                    </el-popover>
                  </template>
                </div>
                <span v-else>-</span>
              </div>
              <div class="virtual-table-cell image-cell">
                <div v-if="item.attachment_url && item.attachment_url.length > 0">
                  <el-button type="text" @click="previewImages(item.attachment_url)">
                    查看图片({{ item.attachment_url.length }})
                  </el-button>
                </div>
                <span v-else>-</span>
              </div>
              <div class="virtual-table-cell time-cell">
                {{ item.processing_time || '-' }}
              </div>
              <div class="virtual-table-cell delay-status-cell">
                <el-tag :type="item.is_delayed ? 'danger' : 'success'">
                  {{ item.is_delayed ? '是' : '否' }}
                </el-tag>
              </div>
              <div class="virtual-table-cell delay-time-cell">
                {{ item.delay_time || '-' }}
              </div>
            </div>
          </RecycleScroller>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showAssignmentDetailDialog = false">关 闭</el-button>
        </span>
      </el-dialog>

    </el-card>
  </div>
</template>

<script>
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

export default {
  components: {
    RecycleScroller
  },
  data() {
    return {
      model: 'assignment',
      queryCreateDate: this.getLastMonthRange(),
      queryDeadlineDate: [],
      assignmentList: [], // 任务列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      can_add: window.localStorage.getItem('can_add') === 'true', // 是否可以新增任务
      is_white_user: false, // 是否可以查看全部任务
      queryInfo: { // 查询栏
        title: '', // 任务标题
        description: '', // 任务描述
        priority: '', // 优先级
        status: '', // 任务状态
        category: '', // 任务类型
        start_date: '', // 发布时间-开始时间
        end_date: '', // 发布时间-结束时间
        deadline_start: '', // 截止时间-开始时间
        deadline_end: '', // 截止时间-结束时间
        send_from: '', // 发布人
        pagenum: 1,
        pagesize: 20
      },
      total: 0,
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      // 新增任务相关数据
      showAddAssignmentDialog: false, // 控制新增任务弹框显示
      showConfirmDialog: false, // 控制二次确认弹框显示
      assignmentForm: {
        title: '',
        description: '',
        priority: '',
        category: '',
        comp_users: [], // 处理人列表
        deadline: '',
        attachment_url: [] // 上传的图片URL列表
      },
      // 人员选择相关
      employeeOptions: [], // 员工选项列表
      employeeLoading: false, // 员工搜索加载状态
      // 员工选择弹框相关
      showEmployeeSelectDialog: false, // 控制员工选择弹框显示
      employeeList: [], // 员工列表数据
      employeeTotal: 0, // 员工总数
      selectedEmployees: [], // 当前页面已选择的员工列表（完整的员工对象）
      selectedUsers: [], // 全局选择状态管理，存储所有已勾选员工的emp_account
      employeeQueryInfo: { // 员工查询参数
        pagenum: 1,
        pagesize: 20,
        emp_account: '',
        emp_name: '',
        dept_name: ''
      },
      employeeFilter: { // 员工筛选条件
        emp_account: '',
        emp_name: '',
        dept_name: ''
      },
      employeeFilterTimer: null, // 员工筛选防抖定时器
      isRestoringSelection: false, // 标记是否正在恢复选择状态，防止触发选择变化事件
      // 图片上传相关
      uploadFileList: [], // 上传文件列表
      maxPersonLimit: '', // 最大发送人数限制
      // 任务详情相关
      showAssignmentDetailDialog: false, // 控制任务详情弹框显示
      assignmentDetail: null, // 当前查看的任务详情
      handlerSearchQuery: '', // 处理人搜索关键词
      handlerStatusFilter: '', // 处理人状态筛选
      selectedHandlers: [], // 选中的处理人
      filteredHandlers: [], // 过滤后的处理人列表
      showBatchUrgeDialog: false, // 批量催促确认弹框
      isUrgeDisabled: false, // 催促按钮是否禁用
      // 虚拟滚动相关
      estimatedItemSize: 60, // 预估每行高度
      checkAll: false, // 全选状态
      isIndeterminate: false, // 半选状态
      minTableHeight: 120, // 最小表格高度（表头 + 至少2行数据）
      maxTableHeight: 400, // 最大表格高度
      // 新建任务表单验证规则
      assignmentFormRules: {
        title: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { max: 100, message: '任务名称不能超过100个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入任务描述', trigger: 'blur' },
          { max: 2000, message: '任务描述不能超过2000个字符', trigger: 'blur' }
        ],
        priority: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ],
        category: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        comp_users: [
          { required: true, message: '请选择处理人', trigger: 'change' }
        ],
        deadline: [
          { required: true, message: '请选择截止时间', trigger: 'change' },
          { validator: this.validateDeadline, trigger: 'change' }
        ]
      }
    }
  },
  /**
   * 组件创建时的生命周期钩子
   * 初始化默认日期并获取任务列表
   */
  async created() {
    await this.isWhiteUser()
    this.default_data()
    this.getAssignmentList()
  },
  /**
   * 组件挂载后的生命周期钩子
   * 添加键盘事件监听器用于图片预览功能
   */
  mounted() {
    // 添加键盘事件监听器
    document.addEventListener('keydown', this.handleKeydown)
  },
  /**
   * 组件销毁前的生命周期钩子
   * 移除键盘事件监听器防止内存泄漏
   */
  beforeDestroy() {
    // 移除键盘事件监听器
    document.removeEventListener('keydown', this.handleKeydown)
  },
  computed: {
    /**
     * 当前预览图片的URL
     * @returns {string} 当前图片的URL或空字符串
     */
    currentImageSrc() {
      return this.previewImagesList[this.currentImageIndex] || ''
    },
    /**
     * 任务图片上传路径
     * @returns {string} 完整的图片上传API路径
     */
    assignmentUploadPath() {
      return `${this.$http.defaults.baseURL || ''}question/question_attachment_upload/`
    },
    /**
     * 截止时间选择器配置
     * @returns {Object} 日期选择器的配置对象
     */
    deadlinePickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      }
    },
    /**
     * 选中的处理人ID列表
     * @returns {Array<string>} 处理人ID数组
     */
    selectedHandlerIds() {
      return this.selectedHandlers.map(handler => handler.comp_user)
    },
    /**
     * 计算虚拟表格的动态高度
     * 根据处理人数量动态计算表格高度，限制在最小和最大高度之间
     * @returns {number} 计算后的表格高度（像素）
     */
    dynamicTableHeight() {
      if (!this.filteredHandlers || this.filteredHandlers.length === 0) {
        return this.minTableHeight
      }
      // 计算所需高度：表头高度(60px) + 数据行高度
      const headerHeight = 60
      const dataRowsHeight = this.filteredHandlers.length * this.estimatedItemSize
      const totalHeight = headerHeight + dataRowsHeight
      // 限制在最小和最大高度之间
      return Math.min(Math.max(totalHeight, this.minTableHeight), this.maxTableHeight)
    }
  },
  methods: {
    // ==================== 表单验证相关方法 ====================

    /**
     * 自定义校验规则：检查截止时间是否大于当前时间
     * @param {Object} _rule - 验证规则对象（未使用）
     * @param {string} value - 待验证的值
     * @param {Function} callback - 回调函数
     * @returns {void}
     */
    validateDeadline(_rule, value, callback) {
      if (!value) {
        return callback(new Error('请填写截止时间'))
      }
      const currentTime = new Date()
      const deadline = new Date(value)
      if (deadline <= currentTime) {
        callback(new Error('截止时间必须大于当前时间'))
      } else {
        callback()
      }
    },

    // ==================== 查询和列表管理相关方法 ====================
    /**
     * 查询是否为白名单用户
     * @returns {void}
     */ async isWhiteUser() {
      try {
        this.queryInfo.pagefrom = 'publish_tasks'
        const { data: res } = await this.$http.post(this.model + '/assignment_list/', this.queryInfo)
        if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
        this.is_white_user = res.data.is_white_user
        console.log('isWhiteUser', this.is_white_user)
      } catch (error) {
        console.warn('API调用失败:', error)
      }
    },

    /**
     * 查询任务列表
     * 重置页码为1并获取任务列表数据
     * @returns {void}
     */
    queryList() {
      this.queryInfo.pagenum = 1
      this.getAssignmentList()
    },

    /**
     * 重置查询条件
     * 将所有查询条件重置为默认值并重新获取数据
     * @returns {void}
     */
    queryReset() {
      this.queryCreateDate = this.getLastMonthRange()
      this.queryDeadlineDate = []
      this.queryInfo = {
        title: '',
        description: '',
        priority: '',
        status: '',
        category: '',
        start_date: '', // 创建时间-开始时间
        end_date: '', // 创建时间-结束时间
        deadline_start: '', // 截止时间-开始时间
        deadline_end: '', // 截止时间-结束时间
        send_from: '', // 发布人
        pagenum: 1,
        pagesize: 20
      }
      this.default_data() // 重置时不设置默认发布人
      this.getAssignmentList()
    },

    /**
     * 获取任务列表数据
     * 调用后端API获取任务列表，根据当前查询条件进行筛选
     * @returns {Promise<void>} 无返回值，结果存储在assignmentList中
     * @throws {Error} 当API调用失败时在控制台输出警告
     */
    async getAssignmentList() {
      try {
        this.queryInfo.pagefrom = 'publish_tasks'
        const { data: res } = await this.$http.post(this.model + '/assignment_list/', this.queryInfo)
        if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
        this.total = res.data.total
        this.assignmentList = res.data.assignments.map(item => ({
          ...item
        }))
      } catch (error) {
        console.warn('API调用失败:', error)
      }
    },

    /**
     * 导出任务列表到Excel文件
     * 调用后端接口导出当前查询条件下的所有任务数据
     * @returns {Promise<void>} 无返回值，成功时自动下载文件
     * @throws {Error} 当导出失败时显示错误消息
     */
    async queryExport() {
      try {
        // 深拷贝 queryInfo，避免修改原始对象
        const exportQueryInfo = JSON.parse(JSON.stringify(this.queryInfo))
        exportQueryInfo.pagesize = 10000
        const response = await this.$http.post(this.model + '/export_assignment_data/', exportQueryInfo, {
          responseType: 'blob'
        })
        // 检查响应是否为 Blob 类型
        if (response.data instanceof Blob && response.data.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          const filename = '任务列表.xlsx'
          await this.downloadFile(response, filename)
        } else {
          // 如果不是预期的 Excel 文件，尝试解析为 JSON
          const text = await response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    /**
     * 下载文件到本地
     * 创建临时下载链接并触发文件下载
     * @param {Object} response - HTTP响应对象，包含文件数据
     * @param {string} filename - 下载文件的名称
     * @returns {Promise<void>} 无返回值，成功时显示成功消息
     */
    async downloadFile(response, filename) {
      const blob = response.data
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      this.$message.success('导出成功')
    },

    /**
     * 处理分页大小变化事件
     * 当用户改变每页显示条数时触发
     * @param {number} newSize - 新的每页显示条数
     * @returns {void}
     */
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.queryInfo.pagenum = 1
      this.getAssignmentList()
    },

    /**
     * 处理页码变化事件
     * 当用户切换页面时触发
     * @param {number} newPage - 新的页码
     * @returns {void}
     */
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getAssignmentList()
    },

    /**
     * 处理创建日期范围变化事件
     * 当用户选择创建时间范围时触发，更新查询条件
     * @param {Array|null} value - 日期范围数组[开始日期, 结束日期]，或null
     * @returns {void}
     */
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.queryInfo.start_date = value[0]
        this.queryInfo.end_date = value[1]
      } else {
        this.queryInfo.start_date = ''
        this.queryInfo.end_date = ''
      }
    },

    /**
     * 处理截止日期范围变化事件
     * 当用户选择截止时间范围时触发，更新查询条件
     * @param {Array|null} value - 日期范围数组[开始日期, 结束日期]，或null
     * @returns {void}
     */
    handleDeadlineDateChange(value) {
      if (value && value.length === 2) {
        this.queryInfo.deadline_start = value[0]
        this.queryInfo.deadline_end = value[1]
      } else {
        this.queryInfo.deadline_start = ''
        this.queryInfo.deadline_end = ''
      }
    },

    /**
     * 设置默认条件
     * @returns {void}
     */
    default_data() {
      console.log('default_data', this.is_white_user)
      if (this.queryCreateDate && this.queryCreateDate.length === 2) {
        this.queryInfo.start_date = this.queryCreateDate[0]
        this.queryInfo.end_date = this.queryCreateDate[1]
      }
      if (this.is_white_user) {
        this.queryInfo.send_from = this.chinese_name
      }
    },

    // ==================== 任务基本操作方法 ====================

    /**
     * 打开新增任务弹框
     * 显示任务创建对话框并重置表单数据
     * @returns {void}
     */
    publishAssignment() {
      // 获取最大发送人数限制
      this.getMaxPersonLimit()
      this.showAddAssignmentDialog = true
      this.resetAssignmentForm()
    },

    /**
     * 打开员工选择弹框
     * 显示员工选择对话框并初始化数据
     * @returns {void}
     */
    openEmployeeSelectDialog() {
      this.showEmployeeSelectDialog = true

      // 重置筛选条件（工号输入框保持为空，不显示已选择的员工工号）
      this.employeeFilter = {
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }
      this.employeeQueryInfo = {
        pagenum: 1,
        pagesize: 20,
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }

      // 初始化selectedUsers数组，将assignmentForm.comp_users的值赋给selectedUsers作为初始选择状态
      this.selectedUsers = []
      if (this.assignmentForm.comp_users && this.assignmentForm.comp_users.length > 0) {
        this.selectedUsers = [...this.assignmentForm.comp_users]
      }

      // 清空当前页面选择状态，准备重新恢复
      this.selectedEmployees = []
      // 重置恢复状态标志
      this.isRestoringSelection = false

      // 获取员工列表
      // 如果有已选择的员工，则显示这些员工（通过emp_account参数）
      // 如果没有已选择的员工，则不传递任何查询条件，显示空列表等待用户查询
      this.getEmployeeList()
    },

    /**
     * 查看任务详情
     * 获取指定任务的详细信息并打开详情弹框
     * @param {Object} assignment - 任务对象，包含任务ID等信息
     * @returns {Promise<void>} 无返回值，成功时打开详情弹框
     * @throws {Error} 当获取详情失败时在控制台输出错误
     */
    async viewAssignmentDetail(assignment) {
      try {
        // 调用详情接口获取任务详情
        const { data: res } = await this.$http.get(`${this.model}/assignment_detail/?id=${assignment.id}`)

        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '获取任务详情失败')
        }

        this.assignmentDetail = res.data
        // 初始化处理人列表，添加展开状态属性
        if (this.assignmentDetail.comp_infos) {
          this.assignmentDetail.comp_infos = this.assignmentDetail.comp_infos.map(handler => ({
            ...handler,
            showFullReply: false // 用于控制回复内容的展开/收起
          }))
        }

        this.filteredHandlers = this.assignmentDetail.comp_infos || []
        this.selectedHandlers = []
        this.handlerSearchQuery = ''
        this.handlerStatusFilter = ''
        // 初始化虚拟滚动状态
        this.checkAll = false
        this.isIndeterminate = false
        this.showAssignmentDetailDialog = true

        // 确保虚拟滚动器正确初始化
        this.$nextTick(() => {
          if (this.$refs.handlerScroller) {
            this.$refs.handlerScroller.scrollToItem(0)
          }
        })
      } catch (error) {
        console.error('获取任务详情失败:', error)
      }
    },

    /**
     * 关闭任务
     * 将指定任务的状态更新为已关闭
     * @param {Object} assignment - 任务对象，包含任务ID
     * @returns {Promise<void>} 无返回值，成功时刷新任务列表
     * @throws {Error} 当关闭失败时显示错误消息
     */
    async closeAssignment(assignment) {
      try {
        const { data: res } = await this.$http.post(this.model + '/update_assignment_status/', {
          id: assignment.id,
          status: 2,
          is_delete: false
        })
        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '关闭任务失败')
        }
        this.$message.success('任务关闭成功')
        await this.getAssignmentList() // 刷新列表
      } catch (error) {
        console.error('关闭任务失败:', error)
        this.$message.error('关闭任务失败，请稍后重试')
      }
    },

    /**
     * 删除任务
     * 将指定任务标记为已删除
     * @param {Object} assignment - 任务对象，包含任务ID
     * @returns {Promise<void>} 无返回值，成功时刷新任务列表
     * @throws {Error} 当删除失败时显示错误消息
     */
    async deleteAssignment(assignment) {
      try {
        const { data: res } = await this.$http.post(this.model + '/update_assignment_status/', {
          id: assignment.id,
          is_delete: true
        })
        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '删除任务失败')
        }
        this.$message.success('任务删除成功')
        await this.getAssignmentList() // 刷新列表
      } catch (error) {
        console.error('删除任务失败:', error)
        this.$message.error('删除任务失败，请稍后重试')
      }
    },

    // ==================== 工具和辅助方法 ====================

    /**
     * 获取最近一个月的日期范围
     * 计算从30天前到今天的日期范围
     * @returns {Array<string>} 格式化后的日期范围数组[开始日期, 结束日期]
     */
    getLastMonthRange() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [this.formatDate(start), this.formatDate(end)]
    },

    /**
     * 格式化日期为YYYY-MM-DD格式
     * @param {Date} date - 需要格式化的日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 获取任务类型标签文本
     * 根据任务类型数值返回对应的中文标签
     * @param {number} assignmentType - 任务类型数值(0-4)
     * @returns {string} 任务类型的中文标签
     */
    getAssignmentTypeLabel(assignmentType) {
      const labelMap = {
        0: '客户经营',
        1: '工单处理',
        2: '款项跟进',
        3: '公司事项',
        4: '其他'
      }
      return labelMap[assignmentType] || '其他'
    },
    /**
     * 获取优先级标签样式类型
     * 根据优先级数值返回对应的Element UI标签样式
     * @param {number} priority - 优先级数值(0-3)
     * @returns {string} Element UI标签样式类型
     */
    getPriorityTagType(priority) {
      const typeMap = {
        0: 'danger',
        1: 'warning',
        2: 'primary',
        3: 'info'
      }
      return typeMap[priority] || 'info'
    },
    /**
     * 获取优先级标签文本
     * 根据优先级数值返回对应的中文标签
     * @param {number} priority - 优先级数值(0-3)
     * @returns {string} 优先级的中文标签
     */
    getPriorityLabel(priority) {
      const labelMap = {
        0: '紧急',
        1: '高',
        2: '中',
        3: '低'
      }
      return labelMap[priority] || '低'
    },
    /**
     * 获取任务状态标签样式类型
     * 根据任务状态数值返回对应的Element UI标签样式
     * @param {number} status - 任务状态数值(0-3)
     * @returns {string} Element UI标签样式类型
     */
    getStatusTagType(status) {
      const typeMap = {
        0: 'primary',
        1: 'success',
        2: 'info',
        3: 'danger'
      }
      return typeMap[status] || 'primary'
    },
    /**
     * 获取任务状态标签文本
     * 根据任务状态数值返回对应的中文标签
     * @param {number} status - 任务状态数值(0-3)
     * @returns {string} 任务状态的中文标签
     */
    getStatusLabel(status) {
      const labelMap = {
        0: '进行中',
        1: '已完成',
        2: '已关闭',
        3: '已延期'
      }
      return labelMap[status] || '进行中'
    },
    /**
     * 预览图片
     * 打开图片预览弹框并显示指定的图片列表
     * @param {Array<string>} images - 图片URL数组
     * @returns {void}
     */
    previewImages(images) {
      this.previewImagesList = images
      this.currentImageIndex = 0
      this.dialogVisible = true
    },
    /**
     * 关闭图片预览弹框
     * 关闭图片预览弹框并清理相关数据
     * @returns {void}
     */
    closeImagePreview() {
      this.dialogVisible = false
      this.previewImagesList = []
      this.currentImageIndex = 0
    },
    /**
     * 切换到上一张图片
     * 在图片预览模式下切换到前一张图片
     * @returns {void}
     */
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    /**
     * 切换到下一张图片
     * 在图片预览模式下切换到后一张图片
     * @returns {void}
     */
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    /**
     * 键盘事件处理器
     * 监听左右箭头键实现图片切换功能，ESC键关闭弹框
     * @param {KeyboardEvent} event - 键盘事件对象
     * @returns {void}
     */
    handleKeydown(event) {
      // 只在图片预览弹框打开时处理键盘事件
      if (!this.dialogVisible) return

      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'Escape') {
        this.closeImagePreview()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },

    /**
     * 重置任务表单
     * 清空任务表单的所有数据并重置相关组件状态
     * @returns {void}
     */
    resetAssignmentForm() {
      this.assignmentForm = {
        title: '',
        description: '',
        priority: '',
        category: '',
        comp_users: [],
        deadline: '',
        attachment_url: []
      }
      this.uploadFileList = []
      this.employeeOptions = []

      // 重置员工选择相关数据
      this.selectedEmployees = []
      this.selectedUsers = [] // 清空全局选择状态
      this.employeeList = []
      this.employeeTotal = 0
      this.employeeFilter = {
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }
      this.employeeQueryInfo = {
        pagenum: 1,
        pagesize: 20,
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }

      // 清理上传组件状态
      if (this.$refs.assignmentImageUploader) {
        this.$refs.assignmentImageUploader.clearFiles()
      }

      if (this.$refs.assignmentFormRef) {
        this.$refs.assignmentFormRef.clearValidate()
      }
    },

    /**
     * 处理新增任务弹框关闭事件
     * 当弹框关闭时重置表单数据
     * @returns {void}
     */
    handleAddAssignmentDialogClose() {
      this.resetAssignmentForm()
    },

    /**
     * 搜索员工信息
     * 根据员工姓名关键词搜索员工列表
     * @param {string} query - 搜索关键词
     * @returns {Promise<void>} 无返回值，结果存储在employeeOptions中
     * @throws {Error} 当搜索失败时在控制台输出错误
     */
    async searchEmployees(query) {
      if (!query) {
        this.employeeOptions = []
        return
      }

      this.employeeLoading = true
      try {
        const { data: res } = await this.$http.get('question/get_emp_info/', {
          params: { empname: query }
        })

        if (res.meta.status === 200) {
          this.employeeOptions = res.data.map(item => ({
            value: item.emp_account,
            label: `${item.emp_name}(${item.emp_account})`
          }))
        } else {
          this.employeeOptions = []
        }
      } catch (error) {
        console.error('搜索员工失败:', error)
        this.employeeOptions = []
      } finally {
        this.employeeLoading = false
      }
    },

    /**
     * 处理任务处理人选择变化事件
     * 检查选择的处理人数量是否超过限制
     * @param {Array<string>} selectedValues - 选中的处理人ID数组
     * @returns {void}
     */
    handleAssigneesChange(selectedValues) {
      // 检查人数限制
      if (selectedValues.length > this.maxPersonLimit) {
        this.$message.warning(`当前发送人数超过上限，最多只能选择${this.maxPersonLimit}人`)
        // 移除超出限制的选项
        this.assignmentForm.comp_users = selectedValues.slice(0, this.maxPersonLimit)
      }
    },

    // ==================== 员工选择弹框相关方法 ====================

    /**
     * 获取员工列表数据
     * 调用后端API获取员工列表，支持分页和筛选
     * @returns {Promise<void>} 无返回值，结果存储在employeeList中
     * @throws {Error} 当API调用失败时在控制台输出错误
     */
    async getEmployeeList() {
      try {
        const params = {
          pagenum: this.employeeQueryInfo.pagenum,
          pagesize: this.employeeQueryInfo.pagesize
        }

        // 构建工号数组，包含已选择的员工工号和查询条件中的工号
        const empAccountList = []

        // 检查是否有查询条件
        const hasQueryConditions = this.employeeQueryInfo.emp_account ||
                                  this.employeeQueryInfo.emp_name ||
                                  this.employeeQueryInfo.dept_name

        // 如果有查询条件，则按查询条件查询
        if (hasQueryConditions) {
          // 如果查询条件中有工号，添加到数组中
          if (this.employeeQueryInfo.emp_account) {
            empAccountList.push(this.employeeQueryInfo.emp_account)
          }

          // 将工号数组作为查询参数传递（如果有的话）
          if (empAccountList.length > 0) {
            params.emp_account = empAccountList
          }

          // 添加其他筛选条件
          if (this.employeeQueryInfo.emp_name) {
            params.emp_name = this.employeeQueryInfo.emp_name
          }
          if (this.employeeQueryInfo.dept_name) {
            params.dept_name = this.employeeQueryInfo.dept_name
          }
        } else {
          // 如果没有查询条件，则显示已选择的员工（如果有的话）
          if (this.selectedUsers && this.selectedUsers.length > 0) {
            empAccountList.push(...this.selectedUsers)
            params.emp_account = empAccountList
          } else {
            // 如果既没有查询条件也没有已选择的员工，则返回空列表
            this.employeeList = []
            this.employeeTotal = 0
            return
          }
        }

        const { data: res } = await this.$http.post(`${this.model}/get_dept_emps/`, params)

        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '获取员工列表失败')
        }

        this.employeeList = res.data.emp_infos || []
        this.employeeTotal = res.data.total || 0

        // 恢复之前的选择状态
        this.$nextTick(() => {
          this.restoreEmployeeSelection()
        })
      } catch (error) {
        console.error('获取员工列表失败:', error)
        this.$message.error('获取员工列表失败，请稍后重试')
      }
    },

    async getMaxPersonLimit() {
      try {
        const { data: res } = await this.$http.get(`${this.model}/assignment_user_limit/`)
        if (res.meta.status !== 200) return this.$message.error('获取最大发送人数限制失败')
        this.maxPersonLimit = res.data.assignment_limit
      } catch (error) {
        console.error('获取最大发送人数限制失败:', error)
        this.$message.error('获取最大发送人数限制失败，请稍后重试')
      }
    },

    /**
     * 恢复员工选择状态
     * 在数据加载后恢复之前的选择状态
     * @returns {void}
     */
    restoreEmployeeSelection() {
      if (this.$refs.employeeTable && this.employeeList.length > 0) {
        // 设置恢复状态标志，防止触发选择变化事件
        this.isRestoringSelection = true

        // 清空当前页面的选择状态
        this.selectedEmployees = []

        // 遍历当前页面的员工，检查其emp_account是否在selectedUsers中
        this.employeeList.forEach(employee => {
          if (employee && employee.emp_account && this.selectedUsers.includes(employee.emp_account)) {
            // 设置为勾选状态
            this.$refs.employeeTable.toggleRowSelection(employee, true)
            // 添加到当前页面选择数组
            this.selectedEmployees.push(employee)
          }
        })

        // 恢复状态标志
        this.$nextTick(() => {
          this.isRestoringSelection = false
        })
      }
    },

    /**
     * 处理员工筛选输入
     * 防抖处理用户输入，但不自动执行搜索，需要用户手动点击查询按钮
     * @returns {void}
     */
    handleEmployeeFilter() {
      // 清除之前的定时器
      if (this.employeeFilterTimer) {
        clearTimeout(this.employeeFilterTimer)
      }

      // 不再自动执行搜索，用户需要手动点击查询按钮
      // 这样可以确保用户明确知道需要填写查询条件
    },

    /**
     * 搜索员工列表
     * 根据筛选条件搜索员工，必须至少填写一个查询条件
     * @returns {void}
     */
    searchEmployeeList() {
      // 验证查询条件：必须至少填写工号、员工姓名、所属部门中的一个
      const hasEmpAccount = this.employeeFilter.emp_account && this.employeeFilter.emp_account.trim()
      const hasEmpName = this.employeeFilter.emp_name && this.employeeFilter.emp_name.trim()
      const hasDeptName = this.employeeFilter.dept_name && this.employeeFilter.dept_name.trim()

      if (!hasEmpAccount && !hasEmpName && !hasDeptName) {
        this.$message.warning('请至少填写工号、员工姓名、所属部门中的一个查询条件')
        return
      }

      // 设置保护标志，防止搜索过程中的选择变化事件影响全局状态
      this.isRestoringSelection = true
      // 更新查询参数
      this.employeeQueryInfo.emp_account = this.employeeFilter.emp_account
      this.employeeQueryInfo.emp_name = this.employeeFilter.emp_name
      this.employeeQueryInfo.dept_name = this.employeeFilter.dept_name
      this.employeeQueryInfo.pagenum = 1

      this.getEmployeeList()
    },

    /**
     * 重置员工筛选条件
     * 清空所有筛选条件并重新获取数据，回到初始状态
     * @returns {void}
     */
    resetEmployeeFilter() {
      // 设置保护标志，防止重置过程中的选择变化事件影响全局状态
      this.isRestoringSelection = true
      this.employeeFilter = {
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }
      this.employeeQueryInfo = {
        pagenum: 1,
        pagesize: 20,
        emp_account: '',
        emp_name: '',
        dept_name: ''
      }
      // 重置后显示已选择的员工（如果有的话）
      this.getEmployeeList()
    },

    /**
     * 处理员工分页大小变化
     * @param {number} newSize - 新的每页显示条数
     * @returns {void}
     */
    handleEmployeeSizeChange(newSize) {
      // 设置保护标志，防止翻页过程中的选择变化事件影响全局状态
      this.isRestoringSelection = true
      this.employeeQueryInfo.pagesize = newSize
      this.employeeQueryInfo.pagenum = 1
      this.getEmployeeList()
    },

    /**
     * 处理员工页码变化
     * @param {number} newPage - 新的页码
     * @returns {void}
     */
    handleEmployeeCurrentChange(newPage) {
      // 设置保护标志，防止翻页过程中的选择变化事件影响全局状态
      this.isRestoringSelection = true
      this.employeeQueryInfo.pagenum = newPage
      this.getEmployeeList()
    },

    /**
     * 处理员工选择变化
     * 当用户勾选或取消勾选员工时触发
     * @param {Array} selection - 当前页面选中的员工列表
     * @returns {void}
     */
    handleEmployeeSelectionChange(selection) {
      // 如果正在恢复选择状态，忽略此次变化事件
      if (this.isRestoringSelection) {
        return
      }

      // 确保selection是数组
      if (!Array.isArray(selection)) {
        selection = []
      }

      // 找出新增和移除的员工
      const previousSelection = this.selectedEmployees || []

      // 找出新增的员工（在selection中但不在previousSelection中）
      const addedEmployees = selection.filter(emp =>
        emp && emp.emp_account && !previousSelection.some(prev => prev && prev.emp_account === emp.emp_account)
      )

      // 找出移除的员工（在previousSelection中但不在selection中）
      const removedEmployees = previousSelection.filter(prev =>
        prev && prev.emp_account && !selection.some(emp => emp && emp.emp_account === prev.emp_account)
      )

      // 处理新增的员工
      const rejectedEmployees = [] // 记录被拒绝的员工
      addedEmployees.forEach(emp => {
        if (emp && emp.emp_account && !this.selectedUsers.includes(emp.emp_account)) {
          // 检查是否会超过限制
          if (this.selectedUsers.length >= this.maxPersonLimit) {
            rejectedEmployees.push(emp)
          } else {
            this.selectedUsers.push(emp.emp_account)
          }
        }
      })

      // 取消勾选时：从selectedUsers中移除该员工的emp_account
      removedEmployees.forEach(emp => {
        if (emp && emp.emp_account) {
          const index = this.selectedUsers.indexOf(emp.emp_account)
          if (index > -1) {
            this.selectedUsers.splice(index, 1)
          }
        }
      })

      // 如果有员工被拒绝，显示提示信息并恢复表格状态
      if (rejectedEmployees.length > 0) {
        const rejectedNames = rejectedEmployees.map(emp => emp.emp_name).join('、')
        this.$message({
          message: `已达到最大选择人数限制（${this.maxPersonLimit}人），无法勾选：${rejectedNames}`,
          type: 'warning',
          duration: 4000,
          showClose: true
        })

        // 恢复表格选择状态，取消被拒绝员工的勾选
        this.$nextTick(() => {
          this.restoreEmployeeSelection()
        })
        return
      }

      // 更新当前页面选择状态
      this.selectedEmployees = [...selection]
    },

    /**
     * 检查员工是否可选择
     * 用于表格的selectable属性
     * @param {Object} row - 员工数据行
     * @returns {boolean} 是否可选择
     */
    checkEmployeeSelectable(row) {
      // 基本检查：员工数据是否有效
      if (!row || !row.emp_account) {
        return false
      }

      // 如果该员工已经被选中，则可以操作（用于取消勾选）
      if (this.selectedUsers.includes(row.emp_account)) {
        return true
      }

      // 如果已达到最大选择人数限制，且该员工未被选中，则不可选择
      if (this.selectedUsers.length >= this.maxPersonLimit) {
        return false
      }

      // 其他业务逻辑判断（如员工状态、权限等）
      return true
    },

    /**
     * 确认员工选择
     * 将选中的员工应用到任务表单中
     * @returns {void}
     */
    confirmEmployeeSelection() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请至少选择一个处理人')
        return
      }

      // 点击"确认"按钮时：将selectedUsers的值赋给assignmentForm.comp_users
      this.assignmentForm.comp_users = [...this.selectedUsers]

      // 关闭弹框
      this.showEmployeeSelectDialog = false

      // 显示成功提示，包含选择状态信息
      const statusText = this.selectedUsers.length >= this.maxPersonLimit
        ? `（已达到最大限制 ${this.maxPersonLimit} 人）`
        : `（还可选择 ${this.maxPersonLimit - this.selectedUsers.length} 人）`

      this.$message.success(`已选择 ${this.selectedUsers.length} 名处理人${statusText}`)
    },

    /**
     * 清空已选择的员工
     * 一键清空所有已选择的处理人
     * @returns {void}
     */
    clearSelectedEmployees() {
      if (this.assignmentForm.comp_users.length === 0) {
        this.$message.info('当前没有已选择的处理人')
        return
      }

      this.$confirm(`确认清空所有已选择的 ${this.assignmentForm.comp_users.length} 名处理人？`, '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const clearedCount = this.assignmentForm.comp_users.length

        // 清空任务表单中的处理人列表
        this.assignmentForm.comp_users = []
        // 清空全局选择状态
        this.selectedUsers = []
        // 清空当前页面选择的员工数组
        this.selectedEmployees = []

        this.$message.success(`已清空 ${clearedCount} 名处理人`)
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 处理员工选择弹框关闭事件
     * 当弹框关闭时的清理工作
     * @returns {void}
     */
    handleEmployeeSelectDialogClose() {
      // 点击"取消"或关闭弹框时：不修改assignmentForm.comp_users，但需要将selectedUsers重置为assignmentForm.comp_users的值
      this.selectedUsers = []
      if (this.assignmentForm.comp_users && this.assignmentForm.comp_users.length > 0) {
        this.selectedUsers = [...this.assignmentForm.comp_users]
      }

      // 清理定时器
      if (this.employeeFilterTimer) {
        clearTimeout(this.employeeFilterTimer)
        this.employeeFilterTimer = null
      }
    },

    /**
     * 格式化部门信息
     * 将分号分隔的部门信息转换为换行显示
     * @param {string} deptInfo - 部门信息字符串
     * @returns {string} 格式化后的部门信息
     */
    formatDeptInfo(deptInfo) {
      if (!deptInfo) return ''
      // 将分号替换为换行符
      return deptInfo.replace(/;/g, '\n')
    },

    /**
     * 处理任务描述粘贴事件
     * 支持从剪贴板粘贴图片并自动上传
     * @param {ClipboardEvent} event - 粘贴事件对象
     * @returns {Promise<void>} 无返回值，检测到图片时自动上传
     */
    async handleAssignmentDescriptionPaste(event) {
      const items = (event.clipboardData || event.originalEvent.clipboardData).items
      const imageFiles = []

      for (const item of items) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile()
          const isValid = this.beforeAssignmentImageUpload(file)
          if (isValid) {
            imageFiles.push(file)
          }
        }
      }

      if (imageFiles.length > 0) {
        event.preventDefault()
        this.uploadPastedAssignmentImages(imageFiles)
      }
    },

    /**
     * 处理粘贴的图片并自动上传
     * 将粘贴的图片文件上传到服务器
     * @param {Array<File>} files - 图片文件数组
     * @returns {void}
     */
    uploadPastedAssignmentImages(files) {
      // 检查当前已上传的图片数量
      const currentImageCount = this.assignmentForm.attachment_url.length

      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return
      }

      // 计算可以上传的图片数量
      const remainingSlots = 3 - currentImageCount
      const filesToUpload = files.slice(0, remainingSlots)

      if (files.length > remainingSlots) {
        this.$message.warning(`最多只能上传${remainingSlots}张图片，已自动选择前${remainingSlots}张`)
      }

      filesToUpload.forEach(async (file, index) => {
        const tempFile = {
          name: file.name || `pasted-image-${Date.now()}-${index}.png`,
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0
        }

        this.$set(this.uploadFileList, this.uploadFileList.length, tempFile)

        try {
          const formData = new FormData()
          formData.append('file', file)

          const response = await this.$http.post(this.assignmentUploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.uploadFileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            this.handleAssignmentImageUploadSuccess(response.data, tempFile, this.uploadFileList)
          } else {
            // 上传失败，移除临时文件
            const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
            if (fileIndex !== -1) {
              this.uploadFileList.splice(fileIndex, 1)
            }
            this.$message.error('图片上传失败')
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          // 上传失败，移除临时文件
          const fileIndex = this.uploadFileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.uploadFileList.splice(fileIndex, 1)
          }
          this.$message.error('图片上传失败')
        }
      })
    },

    /**
     * 处理图片列表变化事件
     * 当上传组件的文件列表发生变化时触发
     * @param {*} _ - 未使用的参数
     * @param {Array} fileList - 新的文件列表
     * @returns {void}
     */
    handAssignmentImageList(_, fileList) {
      // 更新文件列表
      this.uploadFileList = fileList

      // 额外的数据同步检查：确保 attachment_url 与成功上传的文件保持一致
      const successfulUrls = fileList
        .filter(file => file.status === 'success' && file.url)
        .map(file => file.url)

      // 如果发现不一致，进行同步（这是一个保护措施）
      if (successfulUrls.length !== this.assignmentForm.attachment_url.length) {
        this.assignmentForm.attachment_url = successfulUrls
      }
    },

    /**
     * 任务图片上传前校验
     * 检查图片格式、大小和数量限制
     * @param {File} file - 待上传的文件对象
     * @returns {boolean} 校验通过返回true，否则返回false
     */
    beforeAssignmentImageUpload(file) {
      // 检查图片数量限制
      const currentImageCount = this.assignmentForm.attachment_url.length
      if (currentImageCount >= 3) {
        this.$message.warning('已达到图片上传上限（3张），无法继续上传')
        return false
      }

      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }

      return isValidType && isValidExtension && isLt5M
    },

    /**
     * 任务图片上传成功回调
     * 处理图片上传成功后的响应，更新文件列表和URL数组
     * @param {Object} response - 服务器响应对象
     * @param {Object} file - 上传的文件对象
     * @returns {void}
     */
    handleAssignmentImageUploadSuccess(response, file) {
      if (response.meta.status === 200) {
        // 添加URL到attachment_url数组
        this.assignmentForm.attachment_url.push(response.data.fileUrl)

        // 更新uploadFileList中对应的文件对象
        const index = this.uploadFileList.findIndex(f => f.uid === file.uid)

        if (index !== -1) {
          const updatedFile = {
            ...this.uploadFileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100
          }
          this.$set(this.uploadFileList, index, updatedFile)
        }
      }
    },

    /**
     * 任务图片删除回调
     * 处理用户删除图片时的操作，同步更新相关数据
     * @param {Object} file - 被删除的文件对象
     * @returns {void}
     */
    handleAssignmentImageRemove(file) {
      const index = this.uploadFileList.indexOf(file)

      if (index > -1) {
        // 记录删除前的 attachment_url 内容

        // 获取要删除的文件URL，支持多种情况
        let fileUrl = null

        // 情况1：file.url 存在（正常上传成功的文件）
        if (file.url) {
          fileUrl = file.url
        } else if (file.response && file.response.data && file.response.data.fileUrl) {
          // 情况2：file.response 存在（刚上传成功的文件）
          fileUrl = file.response.data.fileUrl
        }

        // 从 attachment_url 中删除对应的 URL
        let urlDeleted = false
        if (fileUrl) {
          const urlIndex = this.assignmentForm.attachment_url.indexOf(fileUrl)

          if (urlIndex !== -1) {
            this.assignmentForm.attachment_url.splice(urlIndex, 1)
            urlDeleted = true
          } else {
            console.warn('- 警告：在 attachment_url 中未找到匹配的URL')
          }
        }

        // 备用方案：如果无法通过URL删除，通过索引位置删除
        if (!urlDeleted) {
          console.warn('- 启用备用删除方案：通过索引位置删除')
          if (index < this.assignmentForm.attachment_url.length) {
            this.assignmentForm.attachment_url.splice(index, 1)
            urlDeleted = true
          } else {
            console.error('- 错误：索引超出 attachment_url 范围')
          }
        }
        // 删除 uploadFileList 中的文件
        this.uploadFileList.splice(index, 1)
      }
    },

    /**
     * 任务图片超出限制回调
     * 当用户尝试上传超过限制数量的图片时触发
     * @returns {void}
     */
    handleAssignmentImageExceed() {
      const currentCount = this.assignmentForm.attachment_url.length
      this.$message.warning(`已达到图片上传上限（3张），当前已有${currentCount}张图片`)
    },

    /**
     * 任务图片预览
     * 预览当前任务表单中的所有图片
     * @returns {void}
     */
    handleAssignmentImagePreview() {
      // 直接使用现有的图片预览功能
      this.previewImages(this.assignmentForm.attachment_url)
    },

    /**
     * 提交任务表单
     * 验证表单数据并显示确认弹框
     * @returns {void}
     */
    submitAssignmentForm() {
      this.$refs.assignmentFormRef.validate((valid) => {
        if (valid) {
          // 检查人数限制
          if (this.assignmentForm.comp_users.length > this.maxPersonLimit) {
            this.$message.error(`当前发送人数超过上限，最多只能选择${this.maxPersonLimit}人`)
            return
          }

          if (this.assignmentForm.comp_users.length === 0) {
            this.$message.error('请至少选择一个处理人')
            return
          }

          // 显示二次确认弹框
          this.showConfirmDialog = true
        } else {
          this.$message.error('请完善表单信息')
        }
      })
    },

    /**
     * 确认发布任务
     * 调用API创建新任务并发布给选定的处理人
     * @returns {Promise<void>} 无返回值，成功时关闭弹框并刷新列表
     * @throws {Error} 当发布失败时显示错误消息
     */
    async confirmPublishAssignment() {
      try {
        const assignmentData = {
          title: this.assignmentForm.title,
          description: this.assignmentForm.description,
          priority: parseInt(this.assignmentForm.priority),
          category: parseInt(this.assignmentForm.category),
          comp_users: this.assignmentForm.comp_users,
          deadline: this.assignmentForm.deadline,
          attachment_url: this.assignmentForm.attachment_url
        }

        const { data: res } = await this.$http.post(this.model + '/create_assignment/', assignmentData)

        if (res.meta.status !== 200) {
          return this.$message.error(res.meta.msg || '发布任务失败')
        }

        this.$message.success('任务发布成功')
        this.showConfirmDialog = false
        this.showAddAssignmentDialog = false
        this.resetAssignmentForm()
        await this.getAssignmentList() // 刷新任务列表
      } catch (error) {
        console.error('发布任务失败:', error)
        this.$message.error('发布任务失败，请稍后重试')
      }
    },

    // ========== 任务详情相关方法 ==========

    /**
     * 关闭任务详情弹框
     * 重置所有任务详情相关的数据状态
     * @returns {void}
     */
    handleAssignmentDetailDialogClose() {
      this.assignmentDetail = null
      this.filteredHandlers = []
      this.selectedHandlers = []
      this.handlerSearchQuery = ''
      this.handlerStatusFilter = ''
      this.isUrgeDisabled = false
      // 重置虚拟滚动相关状态
      this.checkAll = false
      this.isIndeterminate = false
    },

    /**
     * 过滤处理人列表
     * 根据搜索关键词和状态筛选条件过滤处理人列表
     * @returns {void}
     */
    filterHandlers() {
      let filtered = this.assignmentDetail.comp_infos || []

      // 按搜索关键词过滤
      if (this.handlerSearchQuery) {
        const query = this.handlerSearchQuery.toLowerCase()
        filtered = filtered.filter(handler =>
          handler.comp_user_name.toLowerCase().includes(query) ||
          handler.comp_user.toLowerCase().includes(query)
        )
      }

      // 按状态过滤
      if (this.handlerStatusFilter !== '' && this.handlerStatusFilter !== null && this.handlerStatusFilter !== undefined) {
        filtered = filtered.filter(handler =>
          handler.status === parseInt(this.handlerStatusFilter)
        )
      }

      this.filteredHandlers = filtered
      // 过滤后更新全选状态
      this.updateCheckAllState()

      // 更新虚拟滚动器
      this.$nextTick(() => {
        if (this.$refs.handlerScroller) {
          this.$refs.handlerScroller.scrollToItem(0)
        }
      })
    },

    /**
     * 获取处理人状态标签样式
     * 根据处理人状态返回对应的Element UI标签样式
     * @param {number} status - 处理人状态数值(0-3)
     * @returns {string} Element UI标签样式类型
     */
    getHandlerStatusTagType(status) {
      const typeMap = {
        0: 'primary', // 进行中
        1: 'success', // 已完成
        2: 'info', // 已关闭
        3: 'danger' // 已延期
      }
      return typeMap[status] || 'primary'
    },

    /**
     * 获取处理人状态标签文本
     * 根据处理人状态数值返回对应的中文标签
     * @param {number} status - 处理人状态数值(0-3)
     * @returns {string} 处理人状态的中文标签
     */
    getHandlerStatusLabel(status) {
      const labelMap = {
        0: '进行中',
        1: '已完成',
        2: '已关闭',
        3: '已延期'
      }
      return labelMap[status] || '进行中'
    },

    /**
     * 切换回复内容展开/收起状态
     * 控制长回复内容的显示和隐藏
     * @param {Object} handler - 处理人对象
     * @returns {void}
     */
    toggleReplyContent(handler) {
      handler.showFullReply = !handler.showFullReply
    },

    /**
     * 判断处理人是否可选择
     * 仅进行中和已延期状态的处理人可以被勾选
     * @param {Object} row - 处理人数据行对象
     * @returns {boolean} 可选择返回true，否则返回false
     */
    isHandlerSelectable(row) {
      return row.status === 0 || row.status === 3
    },
    // ========== 虚拟滚动相关方法 ==========

    /**
     * 处理单个处理人选择变化
     * 当用户勾选或取消勾选单个处理人时触发
     * @param {Object} handler - 处理人对象
     * @param {boolean} checked - 是否被选中
     * @returns {void}
     */
    handleSingleHandlerCheck(handler, checked) {
      if (checked) {
        if (!this.selectedHandlerIds.includes(handler.comp_user)) {
          this.selectedHandlers.push(handler)
        }
      } else {
        const index = this.selectedHandlers.findIndex(h => h.comp_user === handler.comp_user)
        if (index > -1) {
          this.selectedHandlers.splice(index, 1)
        }
      }
      this.updateCheckAllState()
    },

    /**
     * 处理全选/取消全选变化
     * 当用户点击全选复选框时触发
     * @param {boolean} checked - 是否全选
     * @returns {void}
     */
    handleCheckAllChange(checked) {
      if (checked) {
        // 全选：选择所有可选择的处理人
        this.selectedHandlers = this.filteredHandlers.filter(handler => this.isHandlerSelectable(handler))
      } else {
        // 取消全选
        this.selectedHandlers = []
      }
      this.updateCheckAllState()
    },

    /**
     * 更新全选状态
     * 根据当前选中的处理人数量更新全选复选框的状态
     * @returns {void}
     */
    updateCheckAllState() {
      const selectableHandlers = this.filteredHandlers.filter(handler => this.isHandlerSelectable(handler))
      const selectedCount = this.selectedHandlers.length
      const selectableCount = selectableHandlers.length

      if (selectedCount === 0) {
        this.checkAll = false
        this.isIndeterminate = false
      } else if (selectedCount === selectableCount) {
        this.checkAll = true
        this.isIndeterminate = false
      } else {
        this.checkAll = false
        this.isIndeterminate = true
      }
    },

    /**
     * 批量催促处理人
     * 调用API批量催促选中的处理人完成任务
     * @returns {Promise<void>} 无返回值，成功时显示成功消息
     * @throws {Error} 当催促失败时在控制台输出错误
     */
    async batchUrge() {
      if (this.selectedHandlers.length === 0) {
        return this.$message.warning('请先选择要催促的处理人')
      }

      this.$confirm(`确认催促选中的 ${this.selectedHandlers.length} 个处理人？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const handlerIds = this.selectedHandlers.map(handler => handler.comp_user)
          const { data: res } = await this.$http.post(`${this.model}/urge_assignment/`, {
            id: this.assignmentDetail.id,
            comp_users: handlerIds
          })

          if (res.meta.status !== 200) {
            return this.$message.error(res.meta.msg || '批量催促失败')
          }

          this.$message.success(`成功催促 ${this.selectedHandlers.length} 人`)
          this.showBatchUrgeDialog = false

          this.selectedHandlers = []
          this.filterHandlers()
          await this.getAssignmentList()
        } catch (error) {
          console.error('批量催促失败:', error)
        }
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 批量移除处理人
     * 显示确认对话框并批量移除选中的任务处理人
     * @returns {Promise<void>} 无返回值，成功时更新处理人列表
     * @throws {Error} 当移除失败时在控制台输出错误
     */
    batchRemove() {
      if (this.selectedHandlers.length === 0) {
        return this.$message.warning('请先选择要移除的处理人')
      }

      this.$confirm(`确认移除选中的 ${this.selectedHandlers.length} 个处理人？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const handlerIds = this.selectedHandlers.map(handler => handler.comp_user)
          const { data: res } = await this.$http.post(`${this.model}/remove_assignment/`, {
            id: this.assignmentDetail.id,
            comp_users: handlerIds
          })

          if (res.meta.status !== 200) {
            return this.$message.error(res.meta.msg || '批量移除失败')
          }

          this.$message.success(`成功移除 ${this.selectedHandlers.length} 人`)

          // 从列表中移除选中的处理人
          this.selectedHandlers.forEach(selectedHandler => {
            const index = this.assignmentDetail.comp_infos.findIndex(h => h.comp_user === selectedHandler.comp_user)
            if (index > -1) {
              this.assignmentDetail.comp_infos.splice(index, 1)
            }
          })

          this.selectedHandlers = []
          this.filterHandlers()
          await this.getAssignmentList()
        } catch (error) {
          console.error('批量移除失败:', error)
        }
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style scoped>

/* 虚拟表格样式 */
.virtual-table-container {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
  background-color: #FFFFFF;
  position: relative;
}

.virtual-table-header {
  background-color: #e6f7ff;
  position: relative;
}

.virtual-table-row {
  display: flex;
  align-items: stretch;
  min-height: 60px;
  position: relative;
}

.header-row {
  font-weight: bold;
  font-size: 14px;
  color: #606266;
  background-color: #e6f7ff;
  height: 60px;
  border-bottom: 1px solid #EBEEF5;
}

.data-row {
  background-color: #FFFFFF;
  transition: background-color 0.25s ease;
  border-bottom: 1px solid #EBEEF5;
}

.data-row:hover {
  background-color: #F5F7FA;
}

.stripe-row {
  background-color: #FAFAFA;
  border-bottom: 1px solid #EBEEF5;
}

.stripe-row:hover {
  background-color: #F5F7FA;
}

.virtual-table-cell {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-word;
  overflow: hidden;
  box-sizing: border-box;
  height: 100%;
  min-height: 60px;
  position: relative;
  border-left: 1px solid #EBEEF5;
}

.virtual-table-cell:first-child {
  border-left: none;
}

.header-row .virtual-table-cell {
  border-left: 1px solid #EBEEF5;
}

.header-row .virtual-table-cell:first-child {
  border-left: none;
}

.virtual-table-body .virtual-table-row:last-child {
  border-bottom: 1px solid #EBEEF5;
}

.selection-cell {
  width: 55px;
  flex-shrink: 0;
}

.handler-cell {
  width: 120px;
  flex-shrink: 0;
}

.status-cell {
  width: 80px;
  flex-shrink: 0;
}

.reply-cell {
  width: 450px;
  flex-shrink: 0;
  text-align: left;
  justify-content: flex-start;
}

/* 回复文本容器样式 */
.reply-text-container {
  width: 100%;
  max-width: 430px; /* 留出一些边距 */
}

/* 回复文本省略号样式 */
.reply-text-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  line-height: 1.4;
  color: #606266;
}

.image-cell {
  width: 100px;
  flex-shrink: 0;
}

.time-cell {
  width: 130px;
  flex-shrink: 0;
}

.delay-status-cell {
  width: 100px;
  flex-shrink: 0;
}

.delay-time-cell {
  width: 130px;
  flex-shrink: 0;
}

.virtual-table-body {
  overflow-y: auto;
  position: relative;
}

/* 图片预览弹框样式 */
.transparent-dialog {
  background-color: rgba(255, 255, 255, 0.13);
  box-shadow: none;
  margin: 0;
  position: fixed;
  top: 0;
}

.dark-popover .el-popover__title {
  color: #FFFFFF;
}

.dark-popover .popper__arrow {
  border-top-color: #303133;
  border-bottom-color: #303133;
}

.dark-popover .popper__arrow::after {
  border-top-color: #303133;
  border-bottom-color: #303133;
}

/* 回复popover特定样式 */
.reply-popover {
  max-width: 800px !important;
}

.reply-popover .el-popover__title {
  color: #FFFFFF;
}

/* 查询表单样式 */
.query-form .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.query-form .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: normal;
  padding-right: 8px;
}

.query-form .el-form-item__content {
  margin-left: 0 !important;
}

/* 任务详情弹框样式 */
.assignment-detail-header {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

/* 字段行对齐样式 */
.field-row-item {
  display: flex;
  align-items: center;
  min-height: 32px;
  gap: 8px;
}

.field-row-item label {
  margin: 0;
  flex-shrink: 0;
  line-height: 32px;
}

.field-text-content {
  line-height: 32px;
  display: inline-flex;
  align-items: center;
}

.field-button-content {
  height: 32px;
  line-height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
}

/* 确保 el-tag 在字段行中的对齐 */
.field-row-item .el-tag {
  height: 24px;
  line-height: 22px;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.handler-search-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
}

/* 员工选择弹框样式 */
.employee-filter-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
}

.employee-filter-section .el-input {
  width: 100%;
}

/* 部门信息样式 */
.dept-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

/* 部门tooltip样式 */
.dept-tooltip {
  max-width: 400px;
  white-space: pre-line;
  word-break: break-all;
}

/* 处理人输入框样式 */
.assignee-input-wrapper {
  position: relative;
  width: 100%;
}

.assignee-select-button {
  width: 100%;
  text-align: left;
  justify-content: flex-start;
  padding-right: 30px; /* 为清空图标留出空间 */
  font-weight: normal; /* 保持文字不加粗，与其他文字样式一致 */
}

.assignee-clear-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #C0C4CC;
  cursor: pointer;
  transition: color 0.3s;
  z-index: 1;
}

.assignee-clear-icon:hover {
  color: #909399;
}

/* 员工选择限制相关样式 */
.employee-selection-limit-warning {
  background-color: #FEF0F0;
  border: 1px solid #FBC4C4;
  color: #F56C6C;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 10px;
}

/* 当达到最大限制时，处理人按钮的样式 */
.assignee-select-button.limit-reached {
  border-color: #F56C6C;
  color: #F56C6C;
}

.assignee-select-button.limit-reached:hover {
  border-color: #F78989;
  color: #F78989;
}

/* 统一 placeholder 字体样式 - 最终解决方案 */

/* 最终解决方案：使用深度选择器强制覆盖 Element UI 样式 */
::v-deep .unified-placeholder .el-input__inner::-webkit-input-placeholder,
::v-deep .unified-placeholder .el-textarea__inner::-webkit-input-placeholder {
  color: #C0C4CC !important;
  font-size: 14px !important;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: 400 !important;
  font-style: normal !important;
  opacity: 1 !important;
}

::v-deep .unified-placeholder .el-input__inner::-moz-placeholder,
::v-deep .unified-placeholder .el-textarea__inner::-moz-placeholder {
  color: #C0C4CC !important;
  font-size: 14px !important;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: 400 !important;
  font-style: normal !important;
  opacity: 1 !important;
}

::v-deep .unified-placeholder .el-input__inner:-ms-input-placeholder,
::v-deep .unified-placeholder .el-textarea__inner:-ms-input-placeholder {
  color: #C0C4CC !important;
  font-size: 14px !important;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: 400 !important;
  font-style: normal !important;
  opacity: 1 !important;
}

::v-deep .unified-placeholder .el-input__inner::placeholder,
::v-deep .unified-placeholder .el-textarea__inner::placeholder {
  color: #C0C4CC !important;
  font-size: 14px !important;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
  font-weight: 400 !important;
  font-style: normal !important;
  opacity: 1 !important;
}
</style>
