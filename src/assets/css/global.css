/* 全局样式表 */
html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
}
.el-breadcrumb {
    margin-bottom: 15px;
    font-size: 12px;
}
.el-card {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15) !important;
}
.el-table {
    margin-top: 15px;
    font-size: 12px;
}
body .el-table th.gutter{
    display: table-cell!important;
}
.el-pagination {
    margin-top: 15px;
}
.el-select {
    width: 100%;
}
.add {
    margin-top: 15px;
}
.web-transfer .el-input {
    width: 90%;
}
.web-transfer .el-transfer-panel {
    width: 300px;
    height: 500px;
}
.el-transfer-panel__list.is-filterable {
    height: 400px;
}
.nav-tabs .el-tabs__item {
    border: 1px solid #e8eaec;
    /* color: #515a6e; */
    background-color: #fff;
    /* margin: 2px 4px 2px 0; */
    /* padding: 0 12px; */
    font-size: 12px;
    border-radius:3px;
  }
.nav-tabs .el-tabs__nav-wrap {
    background-color: #fff;
}
.el-table td, .el-table th{
    padding: 8px 0 !important;
}
.tab-nav {
    width: 100%;
    height: 40px;
    margin-bottom: 15px;
    border: 1px solid #f0f0f0;
    font-size: 12px;
}
.tab-nav .btn {
    float: left;
    width: 26px;
    height: 40px;
    background-color: #fff;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
}
.tab-nav .btn-close {
    border-left: 1px solid #f0f0f0;
}
.tab-nav .tab-nav-box {
    float: left;
    width: calc(100% - 85px);
    height: 32px;
    padding: 4px 2px;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
    background-color: #fff;
}
.tab-nav-box .el-tabs__item {
    height: 32px;
    margin: 0 2px;
    line-height: 32px;
    font-size: 12px;
    border: 1px solid #e8eaec;
    padding: 0 12px !important;
    background-color: #fff;
    color: #515a6e;
    font-weight: 500;
    font-family: Arial,"Microsoft Yahei", "微软雅黑";
}
.tab-nav .el-tabs__active-bar {
    background: transparent;
}
.tab-nav .el-tabs__nav-wrap::after {
    height: 0;
}
.tab-nav .el-tabs__item.is-active {
    color: #2d8cf0;
}
.tab-nav .el-icon-close:before {
    content: "\e78d";
}

/* 全局统一 placeholder 样式 - 解决任务弹框中输入框样式不一致问题 */
.el-input__inner::-webkit-input-placeholder,
.el-textarea__inner::-webkit-input-placeholder {
    color: #C0C4CC !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-weight: normal !important;
    font-style: normal !important;
}

.el-input__inner::-moz-placeholder,
.el-textarea__inner::-moz-placeholder {
    color: #C0C4CC !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-weight: normal !important;
    font-style: normal !important;
}

.el-input__inner:-ms-input-placeholder,
.el-textarea__inner:-ms-input-placeholder {
    color: #C0C4CC !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-weight: normal !important;
    font-style: normal !important;
}

.el-input__inner::placeholder,
.el-textarea__inner::placeholder {
    color: #C0C4CC !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-weight: normal !important;
    font-style: normal !important;
}