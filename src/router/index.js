import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../views/Login.vue'
import Jumps from '../views/Jumps.vue'
import Home from '../views/Home.vue'
import Users from '../views/system/Users.vue'
import Roles from '../views/system/Roles.vue'
import Rights from '../views/system/Rights.vue'
import Question from '../views/afterSales/QuestionList.vue'
import ChatRecordList from '../views/afterSales/ChatRecordList.vue'
import QuestionStatistics from '../views/afterSales/GetQuestionStatistics.vue'
import DataArchiving from '../views/afterSales/DataArchiving.vue'
import RoomInitConfig from '../views/afterSales/RoomInitConfig.vue'
import PublishTasks from '../views/tasks/PublishTasks.vue'
import HandleTasks from '../views/tasks/HandleTasks.vue'
import TasksStatistics from '../views/tasks/TasksStatistics.vue'

Vue.use(VueRouter)

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/', redirect: '/jumps' },
  { path: '/login', component: Login },
  { path: '/jumps', component: Jumps },
  {
    path: '/home',
    component: Home,
    redirect: '/question-list',
    children: [
      { path: '/users', component: Users },
      { path: '/roles', component: Roles },
      { path: '/rights', component: Rights },
      { path: '/question-list', component: Question },
      { path: '/chatRecord-list', component: ChatRecordList },
      { path: '/question-statistics', component: QuestionStatistics, meta: { requiresAuth: true } },
      { path: '/data-archiving', component: DataArchiving, meta: { requiresAuth: true } },
      { path: '/room-init-config', component: RoomInitConfig },
      { path: '/publish-tasks', component: PublishTasks },
      { path: '/handle-tasks', component: HandleTasks },
      { path: '/tasks-statistics', component: TasksStatistics }
    ]
  }
]

const router = new VueRouter({
  routes
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('token') // 假设用token判断登录状态
  if (to.matched.some(route => route.meta.requiresAuth)) {
    if (!isAuthenticated) {
      // 保存完整路径（包含查询参数）
      sessionStorage.setItem('redirect', to.fullPath)
      next(`/login?url=${to.fullPath}`)
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
